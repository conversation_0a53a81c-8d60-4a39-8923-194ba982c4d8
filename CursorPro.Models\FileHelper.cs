using System;
using System.Diagnostics;
using System.IO;
using System.Runtime.Versioning;

namespace CursorPro.Models;

public static class FileHelper
{
	public static bool RemoveReadOnlyAttribute(string filePath)
	{
		try
		{
			if (string.IsNullOrEmpty(filePath))
			{
				return false;
			}
			if (!File.Exists(filePath))
			{
				return true;
			}
			if (OperatingSystem.IsWindows())
			{
				return RemoveReadOnlyAttributeWindows(filePath);
			}
			if (OperatingSystem.IsMacOS())
			{
				return RemoveReadOnlyAttributeMac(filePath);
			}
			if (OperatingSystem.IsLinux())
			{
				return RemoveReadOnlyAttributeLinux(filePath);
			}
			return false;
		}
		catch (Exception)
		{
			return false;
		}
	}

	public static bool IsFileWritable(string filePath)
	{
		try
		{
			if (string.IsNullOrEmpty(filePath))
			{
				return false;
			}
			if (!File.Exists(filePath))
			{
				return false;
			}
			using (File.OpenWrite(filePath))
			{
				return true;
			}
		}
		catch (UnauthorizedAccessException)
		{
			return false;
		}
		catch (IOException)
		{
			return false;
		}
		catch (Exception)
		{
			return false;
		}
	}

	public static bool EnsureFileWritable(string filePath)
	{
		try
		{
			if (!RemoveReadOnlyAttribute(filePath))
			{
				return false;
			}
			return true;
		}
		catch (Exception)
		{
			return false;
		}
	}

	public static bool IsFileReadOnly(string filePath)
	{
		try
		{
			if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
			{
				return false;
			}
			if (OperatingSystem.IsWindows())
			{
				return (File.GetAttributes(filePath) & FileAttributes.ReadOnly) == FileAttributes.ReadOnly;
			}
			try
			{
				return !new FileInfo(filePath).IsReadOnly;
			}
			catch
			{
				return false;
			}
		}
		catch (Exception)
		{
			return false;
		}
	}

	public static string GetFilePermissionInfo(string filePath)
	{
		try
		{
			if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
			{
				return "文件不存在";
			}
			if (OperatingSystem.IsWindows())
			{
				FileAttributes attributes = File.GetAttributes(filePath);
				return $"属性: {attributes}";
			}
			using Process process = new Process();
			process.StartInfo.FileName = "stat";
			process.StartInfo.Arguments = "-c '%a %n' \"" + filePath + "\"";
			process.StartInfo.UseShellExecute = false;
			process.StartInfo.RedirectStandardOutput = true;
			process.StartInfo.CreateNoWindow = true;
			process.Start();
			string text = process.StandardOutput.ReadToEnd();
			process.WaitForExit();
			return string.IsNullOrEmpty(text) ? "无法获取权限信息" : text.Trim();
		}
		catch (Exception ex)
		{
			return "获取权限信息失败: " + ex.Message;
		}
	}

	[SupportedOSPlatform("windows")]
	private static bool RemoveReadOnlyAttributeWindows(string filePath)
	{
		try
		{
			FileAttributes attributes = File.GetAttributes(filePath);
			if ((attributes & FileAttributes.ReadOnly) == FileAttributes.ReadOnly)
			{
				File.SetAttributes(filePath, attributes & ~FileAttributes.ReadOnly);
			}
			return true;
		}
		catch (Exception)
		{
			return false;
		}
	}

	[SupportedOSPlatform("macos")]
	private static bool RemoveReadOnlyAttributeMac(string filePath)
	{
		try
		{
			using Process process = new Process();
			process.StartInfo.FileName = "chmod";
			process.StartInfo.Arguments = "644 \"" + filePath + "\"";
			process.StartInfo.UseShellExecute = false;
			process.StartInfo.RedirectStandardError = true;
			process.StartInfo.CreateNoWindow = true;
			process.Start();
			process.StandardError.ReadToEnd();
			process.WaitForExit();
			if (process.ExitCode == 0)
			{
				return true;
			}
			return false;
		}
		catch (Exception)
		{
			return false;
		}
	}

	[SupportedOSPlatform("linux")]
	private static bool RemoveReadOnlyAttributeLinux(string filePath)
	{
		try
		{
			using Process process = new Process();
			process.StartInfo.FileName = "chmod";
			process.StartInfo.Arguments = "644 \"" + filePath + "\"";
			process.StartInfo.UseShellExecute = false;
			process.StartInfo.RedirectStandardError = true;
			process.StartInfo.CreateNoWindow = true;
			process.Start();
			process.StandardError.ReadToEnd();
			process.WaitForExit();
			if (process.ExitCode == 0)
			{
				return true;
			}
			return false;
		}
		catch (Exception)
		{
			return false;
		}
	}
}
