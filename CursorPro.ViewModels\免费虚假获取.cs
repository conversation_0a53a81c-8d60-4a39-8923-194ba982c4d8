using System;
using System.Threading.Tasks;
using CursorPro.Models;

namespace CursorPro.ViewModels;

public static class 免费虚假获取
{
	public static async Task 执行免费虚假获取(Action<string> 设置状态文本)
	{
		try
		{
			设置状态文本("");
			await Task.Delay(200);
			设置状态文本("获取成功! 和cursor继续对话吧");
		}
		catch (Exception)
		{
			设置状态文本("获取失败");
		}
	}

	public static async Task 执行付费虚假获取(Action<string> 设置状态文本)
	{
		try
		{
			设置状态文本("");
			await Task.Delay(200);
			设置状态文本("获取成功! 和cursor继续对话吧");
		}
		catch (Exception)
		{
			设置状态文本("获取失败");
		}
	}

	public static bool 是否为免费用户()
	{
		try
		{
			return string.IsNullOrEmpty(LocalConfig.读取("CardKey"));
		}
		catch (Exception)
		{
			return true;
		}
	}
}
