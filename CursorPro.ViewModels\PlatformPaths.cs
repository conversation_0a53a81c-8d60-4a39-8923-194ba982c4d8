using System;
using System.IO;
using System.Runtime.Versioning;
using CursorPro.Models;

namespace CursorPro.ViewModels;

public class PlatformPaths
{
	public string DbPath { get; private set; } = string.Empty;

	public string SqlitePath { get; private set; } = string.Empty;

	public string MachineIdPath { get; private set; } = string.Empty;

	public string WorkbenchPath { get; private set; } = string.Empty;

	public string PackageJsonPath { get; private set; } = string.Empty;

	public string MainJsPath { get; private set; } = string.Empty;

	public string UpdaterPath { get; private set; } = string.Empty;

	public string UpdateYmlPath { get; private set; } = string.Empty;

	public string ProductJsonPath { get; private set; } = string.Empty;

	public PlatformPaths()
	{
		InitializePaths();
	}

	private void InitializePaths()
	{
		if (OperatingSystem.IsWindows())
		{
			InitializeWindowsPaths();
		}
		else if (OperatingSystem.IsMacOS())
		{
			InitializeMacPaths();
		}
		else
		{
			SetEmptyPaths();
		}
	}

	private void SetEmptyPaths()
	{
		WorkbenchPath = "";
		PackageJsonPath = "";
		MainJsPath = "";
		ProductJsonPath = "";
		UpdateYmlPath = "";
		UpdaterPath = "";
		DbPath = "";
		SqlitePath = "";
		MachineIdPath = "";
	}

	[SupportedOSPlatform("windows")]
	private void InitializeWindowsPaths()
	{
		string text = FindCursorUserDataPath();
		if (string.IsNullOrEmpty(text))
		{
			text = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Cursor");
		}
		DbPath = Path.Combine(text, "User", "globalStorage", "storage.json");
		SqlitePath = Path.Combine(text, "User", "globalStorage", "state.vscdb");
		MachineIdPath = Path.Combine(text, "machineId");
		string cachedInstallPath = CursorProcessManager.GetCachedInstallPath();
		if (string.IsNullOrEmpty(cachedInstallPath))
		{
			WorkbenchPath = "";
			PackageJsonPath = "";
			MainJsPath = "";
			ProductJsonPath = "";
			UpdateYmlPath = "";
			return;
		}
		WorkbenchPath = Path.Combine(cachedInstallPath, "resources", "app", "out", "vs", "workbench", "workbench.desktop.main.js");
		PackageJsonPath = Path.Combine(cachedInstallPath, "resources", "app", "package.json");
		MainJsPath = Path.Combine(cachedInstallPath, "resources", "app", "out", "main.js");
		ProductJsonPath = Path.Combine(cachedInstallPath, "resources", "app", "product.json");
		UpdaterPath = Path.Combine(text, "cursor-updater");
		UpdateYmlPath = Path.Combine(cachedInstallPath, "resources", "app-update.yml");
	}

	[SupportedOSPlatform("windows")]
	private static string? FindCursorUserDataPath()
	{
		string text = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Cursor");
		if (Directory.Exists(text))
		{
			return text;
		}
		return null;
	}

	[SupportedOSPlatform("macos")]
	private void InitializeMacPaths()
	{
		string folderPath = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
		string path = Path.Combine(folderPath, "Library", "Application Support", "Cursor");
		string path2 = Path.Combine(folderPath, "Library", "Application Support");
		DbPath = Path.Combine(path, "User", "globalStorage", "storage.json");
		SqlitePath = Path.Combine(path, "User", "globalStorage", "state.vscdb");
		MachineIdPath = Path.Combine(path, "machineId");
		WorkbenchPath = Path.Combine("/Applications", "Cursor.app", "Contents", "Resources", "app", "out", "vs", "workbench", "workbench.desktop.main.js");
		PackageJsonPath = Path.Combine("/Applications", "Cursor.app", "Contents", "Resources", "app", "package.json");
		MainJsPath = Path.Combine("/Applications", "Cursor.app", "Contents", "Resources", "app", "out", "main.js");
		ProductJsonPath = Path.Combine("/Applications", "Cursor.app", "Contents", "Resources", "app", "product.json");
		UpdaterPath = Path.Combine(path2, "cursor-updater");
		UpdateYmlPath = Path.Combine("/Applications", "Cursor.app", "Contents", "Resources", "app-update.yml");
	}
}
