using System.ComponentModel;

namespace CommunityToolkit.Mvvm.ComponentModel.__Internals
{
    internal static class __KnownINotifyPropertyChangingArgs
    {
        public static readonly PropertyChangingEventArgs IsProcessing = new PropertyChangingEventArgs("IsProcessing");
        public static readonly PropertyChangingEventArgs 显示恢复购买按钮 = new PropertyChangingEventArgs("显示恢复购买按钮");
        public static readonly PropertyChangingEventArgs CurrentOrderNo = new PropertyChangingEventArgs("CurrentOrderNo");
        public static readonly PropertyChangingEventArgs 价格加载中 = new PropertyChangingEventArgs("价格加载中");
        public static readonly PropertyChangingEventArgs 周卡原价 = new PropertyChangingEventArgs("周卡原价");
        public static readonly PropertyChangingEventArgs 周卡现价 = new PropertyChangingEventArgs("周卡现价");
        public static readonly PropertyChangingEventArgs 月卡原价 = new PropertyChangingEventArgs("月卡原价");
        public static readonly PropertyChangingEventArgs 月卡现价 = new PropertyChangingEventArgs("月卡现价");
        public static readonly PropertyChangingEventArgs 周卡数量 = new PropertyChangingEventArgs("周卡数量");
        public static readonly PropertyChangingEventArgs 月卡数量 = new PropertyChangingEventArgs("月卡数量");
        public static readonly PropertyChangingEventArgs 周卡总计信息 = new PropertyChangingEventArgs("周卡总计信息");
        public static readonly PropertyChangingEventArgs 月卡总计信息 = new PropertyChangingEventArgs("月卡总计信息");
    }
}
