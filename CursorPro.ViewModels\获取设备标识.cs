using System;
using System.Linq;
using System.Net.NetworkInformation;
using CursorPro.Models;

namespace CursorPro.ViewModels;

public class 获取设备标识
{
	private string _机器码 = string.Empty;

	private const string 配置键名 = "app_instance_data";

	public string 获取机器码()
	{
		if (!string.IsNullOrEmpty(_机器码))
		{
			return _机器码;
		}
		_机器码 = 获取物理MAC地址();
		if (string.IsNullOrEmpty(_机器码))
		{
			string text = LocalConfig.读取("app_instance_data");
			if (!string.IsNullOrEmpty(text))
			{
				_机器码 = text;
			}
			else
			{
				_机器码 = 生成随机机器码();
			}
		}
		LocalConfig.写入("app_instance_data", _机器码);
		Info.机器码 = _机器码;
		return _机器码;
	}

	private string 获取物理MAC地址()
	{
		try
		{
			NetworkInterface[] allNetworkInterfaces = NetworkInterface.GetAllNetworkInterfaces();
			NetworkInterface networkInterface = allNetworkInterfaces.Where((NetworkInterface ni) => ni.NetworkInterfaceType == NetworkInterfaceType.Ethernet && ni.OperationalStatus == OperationalStatus.Up && !ni.Description.Contains("virtual", StringComparison.OrdinalIgnoreCase) && !ni.Description.Contains("vmware", StringComparison.OrdinalIgnoreCase) && !ni.Description.Contains("virtualbox", StringComparison.OrdinalIgnoreCase) && !ni.Description.Contains("hyper-v", StringComparison.OrdinalIgnoreCase) && !ni.Description.Contains("loopback", StringComparison.OrdinalIgnoreCase)).FirstOrDefault();
			if (networkInterface != null)
			{
				string text = networkInterface.GetPhysicalAddress().ToString();
				if (!string.IsNullOrEmpty(text) && text != "000000000000")
				{
					return text.ToUpper();
				}
			}
			NetworkInterface networkInterface2 = allNetworkInterfaces.Where((NetworkInterface ni) => ni.OperationalStatus == OperationalStatus.Up && !ni.Description.Contains("virtual", StringComparison.OrdinalIgnoreCase) && !ni.Description.Contains("vmware", StringComparison.OrdinalIgnoreCase) && !ni.Description.Contains("virtualbox", StringComparison.OrdinalIgnoreCase) && !ni.Description.Contains("hyper-v", StringComparison.OrdinalIgnoreCase) && !ni.Description.Contains("loopback", StringComparison.OrdinalIgnoreCase)).FirstOrDefault();
			if (networkInterface2 != null)
			{
				string text2 = networkInterface2.GetPhysicalAddress().ToString();
				if (!string.IsNullOrEmpty(text2) && text2 != "000000000000")
				{
					return text2.ToUpper();
				}
			}
		}
		catch (Exception)
		{
		}
		return string.Empty;
	}

	private static string 生成随机机器码()
	{
		Random random = new Random();
		return new string((from s in Enumerable.Repeat("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789", 10)
			select s[random.Next(s.Length)]).ToArray());
	}
}
