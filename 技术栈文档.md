# CursorPro 技术栈文档

## 编程语言

### 主要语言
- **C# 12.0**：主要开发语言，使用最新语言特性
- **XAML**：用于Avalonia UI界面定义
- **JSON**：数据交换和配置存储格式

### 语言特性使用
- **异步编程**：大量使用async/await模式
- **LINQ**：数据查询和处理
- **泛型**：类型安全的集合和方法
- **特性(Attributes)**：用于代码生成和标记
- **可空引用类型**：C# 8.0+的空安全特性

## 核心框架

### UI框架
- **Avalonia UI 11.x**
  - 跨平台桌面应用框架
  - 类似WPF的XAML语法
  - 支持Windows、macOS、Linux
  - MVVM架构模式支持

### .NET运行时
- **.NET 8.0 (netcoreapp8.0)**
  - 最新的.NET运行时
  - 跨平台支持
  - 高性能和现代化API

## 第三方类库

### UI相关库
```xml
<PackageReference Include="Avalonia" Version="11.x" />
<PackageReference Include="Avalonia.Controls" Version="11.x" />
<PackageReference Include="Avalonia.Desktop" Version="11.x" />
<PackageReference Include="Avalonia.Themes.Fluent" Version="11.x" />
<PackageReference Include="Avalonia.Fonts.Inter" Version="11.x" />
```

### MVVM框架
```xml
<PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.0" />
```
- **功能**：提供MVVM模式支持
- **特性**：
  - `[ObservableProperty]`：自动生成属性通知
  - `[RelayCommand]`：自动生成命令
  - `ViewModelBase`：视图模型基类

### 消息框库
```xml
<PackageReference Include="MessageBox.Avalonia" Version="3.x" />
```
- **功能**：跨平台消息框显示
- **用途**：错误提示、确认对话框

### JSON处理
```xml
<PackageReference Include="System.Text.Json" Version="8.x" />
```
- **功能**：JSON序列化和反序列化
- **用途**：API数据交换、配置文件处理

### HTTP客户端
```xml
<PackageReference Include="System.Net.Http" Version="8.x" />
```
- **功能**：HTTP网络通信
- **用途**：与服务器API交互

## 架构模式

### MVVM (Model-View-ViewModel)
```
View (XAML) ←→ ViewModel (C#) ←→ Model (C#)
     ↑              ↑                ↑
  用户界面      业务逻辑          数据模型
```

### 依赖注入
- 使用Avalonia内置的服务容器
- ViewLocator模式自动绑定View和ViewModel

### 异步编程模式
```csharp
// 示例：异步命令模式
[RelayCommand]
private async Task 执行操作()
{
    try 
    {
        IsLoading = true;
        var result = await HttpService.PostAsync(...);
        // 处理结果
    }
    finally 
    {
        IsLoading = false;
    }
}
```

## 平台特定技术

### Windows平台
- **WinAPI调用**：通过P/Invoke调用Windows API
- **注册表访问**：读取系统配置信息
- **进程管理**：Process类管理外部进程
- **文件系统**：特殊文件夹路径处理

### macOS平台
- **Shell命令**：通过Process执行shell命令
- **应用程序包**：处理.app包结构
- **权限管理**：文件访问权限处理

## 网络通信技术

### HTTP/HTTPS协议
- **RESTful API**：标准REST接口调用
- **JSON数据格式**：统一的数据交换格式
- **异步请求**：非阻塞网络调用

### API端点设计
```csharp
// 试用验证API
POST http://119.45.250.178:5269/api/account
{
    "operation": "verify_trial",
    "machine_code": "...",
    "version": "2.2.3",
    "platform": "Windows"
}

// 付费验证API  
POST http://119.45.250.178:5270/api/paid
{
    "operation": "verify_paid",
    "key": "卡密",
    "machine_code": "...",
    // ...
}
```

## 数据存储技术

### 本地配置存储
- **文件格式**：加密的键值对存储
- **存储位置**：用户配置目录
- **加密方式**：简单的字符串加密

### 临时数据缓存
- **内存缓存**：运行时数据缓存
- **文件缓存**：Cursor路径等信息缓存

## 安全技术

### 设备标识生成
```csharp
// MAC地址获取
NetworkInterface.GetAllNetworkInterfaces()
    .Where(nic => nic.OperationalStatus == OperationalStatus.Up)
    .Select(nic => nic.GetPhysicalAddress().ToString())
```

### 数据加密
- **配置加密**：本地配置文件加密存储
- **通信加密**：HTTPS协议保护数据传输

## 构建和部署技术

### 项目构建
- **MSBuild**：.NET标准构建系统
- **NuGet**：包管理器
- **发布配置**：单文件发布、自包含部署

### 跨平台发布
```bash
# Windows发布
dotnet publish -c Release -r win-x64 --self-contained

# macOS发布  
dotnet publish -c Release -r osx-x64 --self-contained
```

## 代码生成技术

### Source Generators
- **CommunityToolkit.Mvvm**：自动生成MVVM代码
- **Avalonia**：XAML编译时代码生成

### 反编译恢复
- **ILSpy**：.NET程序集反编译工具
- **代码重构**：反编译后的代码整理和优化

## 调试和诊断

### 日志记录
```csharp
// Avalonia内置日志
.LogToTrace(LogEventLevel.Warning)
```

### 异常处理
- **全局异常捕获**：应用级异常处理
- **网络异常**：HTTP请求异常处理
- **文件操作异常**：IO异常处理

## 性能优化技术

### 异步编程
- **ConfigureAwait(false)**：避免死锁
- **Task.Run**：CPU密集型操作异步化
- **CancellationToken**：操作取消支持

### 内存管理
- **IDisposable**：资源释放模式
- **弱引用**：避免内存泄漏
- **对象池**：减少GC压力

## 总结

CursorPro项目采用了现代化的.NET技术栈，主要特点：

1. **跨平台**：基于.NET 8.0和Avalonia UI
2. **现代化**：使用C# 12.0最新特性
3. **异步优先**：全面采用async/await模式
4. **MVVM架构**：清晰的代码组织结构
5. **类型安全**：强类型语言和泛型支持
6. **工具链完善**：成熟的开发和构建工具

技术选型体现了对性能、可维护性和用户体验的综合考虑。