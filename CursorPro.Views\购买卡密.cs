using System;
using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using Avalonia;
using Avalonia.Animation;
using Avalonia.Controls;
using Avalonia.Controls.Documents;
using Avalonia.Controls.Presenters;
using Avalonia.Controls.Primitives;
using Avalonia.Data;
using Avalonia.Input;
using Avalonia.Interactivity;
using Avalonia.Layout;
using Avalonia.Markup.Xaml;
using Avalonia.Markup.Xaml.MarkupExtensions;
using Avalonia.Markup.Xaml.MarkupExtensions.CompiledBindings;
using Avalonia.Markup.Xaml.Templates;
using Avalonia.Markup.Xaml.XamlIl.Runtime;
using Avalonia.Media;
using Avalonia.Media.Immutable;
using Avalonia.Styling;
using CompiledAvaloniaXaml;
using CursorPro.ViewModels;

namespace CursorPro.Views;

[CompilerGenerated]
public class 购买卡密 : Window
{
	[CompilerGenerated]
	private class XamlClosure_1
	{
		public static object Build_1(IServiceProvider P_0)
		{
			CompiledAvaloniaXaml.XamlIlContext.Context<购买卡密> context = CreateContext(P_0);
			return StreamGeometry.Parse("M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z");
		}

		public static CompiledAvaloniaXaml.XamlIlContext.Context<购买卡密> CreateContext(IServiceProvider P_0)
		{
			CompiledAvaloniaXaml.XamlIlContext.Context<购买卡密> context = new CompiledAvaloniaXaml.XamlIlContext.Context<购买卡密>(P_0, new object[1] { _0021AvaloniaResources.NamespaceInfo_003A_002FViews_002F购买卡密_002Eaxaml.Singleton }, "avares://CursorPro/Views/购买卡密.axaml");
			if (P_0 != null)
			{
				object service = P_0.GetService(typeof(IRootObjectProvider));
				if (service != null)
				{
					service = ((IRootObjectProvider)service).RootObject;
					context.RootObject = (购买卡密)service;
				}
			}
			return context;
		}

		public static object Build_2(IServiceProvider P_0)
		{
			CompiledAvaloniaXaml.XamlIlContext.Context<购买卡密> context = CreateContext(P_0);
			context.IntermediateRoot = new Border();
			object obj = context.IntermediateRoot;
			((ISupportInitialize)obj).BeginInit();
			((AvaloniaObject)obj).SetValue(Border.CornerRadiusProperty, new CornerRadius(8.0, 8.0, 8.0, 8.0), BindingPriority.Template);
			((AvaloniaObject)obj).SetValue(Border.BoxShadowProperty, BoxShadows.Parse("0 4 15 0 #40000000"), BindingPriority.Template);
			StyledProperty<IBrush?> backgroundProperty = Border.BackgroundProperty;
			LinearGradientBrush linearGradientBrush = new LinearGradientBrush();
			linearGradientBrush.SetValue(LinearGradientBrush.StartPointProperty, new RelativePoint(0.0, 0.0, RelativeUnit.Relative), BindingPriority.Template);
			linearGradientBrush.SetValue(LinearGradientBrush.EndPointProperty, new RelativePoint(1.0, 1.0, RelativeUnit.Relative), BindingPriority.Template);
			GradientStops gradientStops = linearGradientBrush.GradientStops;
			GradientStop gradientStop = new GradientStop();
			gradientStop.SetValue(GradientStop.ColorProperty, Color.FromUInt32(4279592384u), BindingPriority.Template);
			gradientStop.SetValue(GradientStop.OffsetProperty, 0.0, BindingPriority.Template);
			gradientStops.Add(gradientStop);
			GradientStops gradientStops2 = linearGradientBrush.GradientStops;
			GradientStop gradientStop2 = new GradientStop();
			gradientStop2.SetValue(GradientStop.ColorProperty, Color.FromUInt32(4279060385u), BindingPriority.Template);
			gradientStop2.SetValue(GradientStop.OffsetProperty, 1.0, BindingPriority.Template);
			gradientStops2.Add(gradientStop2);
			((AvaloniaObject)obj).SetValue(backgroundProperty, (IBrush)linearGradientBrush, BindingPriority.Template);
			Grid grid2;
			Grid grid = (grid2 = new Grid());
			((ISupportInitialize)grid).BeginInit();
			((Decorator)obj).Child = grid;
			Controls children = grid2.Children;
			ContentPresenter contentPresenter2;
			ContentPresenter contentPresenter = (contentPresenter2 = new ContentPresenter());
			((ISupportInitialize)contentPresenter).BeginInit();
			children.Add(contentPresenter);
			CompiledAvaloniaXaml.XamlDynamicSetters._003C_003EXamlDynamicSetter_1(contentPresenter2, BindingPriority.Template, new TemplateBinding(ContentControl.ContentProperty).ProvideValue());
			contentPresenter2.SetValue(Layoutable.HorizontalAlignmentProperty, HorizontalAlignment.Center, BindingPriority.Template);
			contentPresenter2.SetValue(Layoutable.VerticalAlignmentProperty, VerticalAlignment.Center, BindingPriority.Template);
			AvaloniaObjectExtensions.Bind(contentPresenter2, Layoutable.MarginProperty, new TemplateBinding(TemplatedControl.PaddingProperty).ProvideValue());
			((ISupportInitialize)contentPresenter2).EndInit();
			((ISupportInitialize)grid2).EndInit();
			((ISupportInitialize)obj).EndInit();
			return obj;
		}
	}

	private 购买卡密VM? _viewModel;

	private static Action<object> _0021XamlIlPopulateOverride;

	public 购买卡密()
	{
		InitializeComponent();
		_viewModel = new 购买卡密VM();
		base.DataContext = _viewModel;
		base.Title = "赞助支持";
		base.Closed += 购买卡密_Closed;
	}

	private void 购买卡密_Closed(object? sender, EventArgs e)
	{
		try
		{
			_viewModel = null;
			base.DataContext = null;
		}
		catch (Exception)
		{
		}
	}

	private void Border_PointerPressed(object sender, PointerPressedEventArgs e)
	{
		BeginMoveDrag(e);
	}

	private void CloseButton_Click(object sender, RoutedEventArgs e)
	{
		try
		{
			_viewModel = null;
			base.DataContext = null;
		}
		catch (Exception)
		{
		}
		finally
		{
			Close();
		}
	}

	[GeneratedCode("Avalonia.Generators.NameGenerator.InitializeComponentCodeGenerator", "11.2.1.0")]
	[ExcludeFromCodeCoverage]
	public void InitializeComponent(bool loadXaml = true)
	{
		if (loadXaml)
		{
			_0021XamlIlPopulateTrampoline(this);
		}
	}

	private unsafe static void _0021XamlIlPopulate(IServiceProvider P_0, 购买卡密 P_1)
	{
		CompiledAvaloniaXaml.XamlIlContext.Context<购买卡密> context = new CompiledAvaloniaXaml.XamlIlContext.Context<购买卡密>(P_0, new object[1] { _0021AvaloniaResources.NamespaceInfo_003A_002FViews_002F购买卡密_002Eaxaml.Singleton }, "avares://CursorPro/Views/购买卡密.axaml")
		{
			RootObject = P_1,
			IntermediateRoot = P_1
		};
		((ISupportInitialize)P_1).BeginInit();
		context.PushParent(P_1);
		P_1.Width = 500.0;
		P_1.Height = 450.0;
		P_1.WindowStartupLocation = WindowStartupLocation.CenterScreen;
		P_1.SystemDecorations = SystemDecorations.None;
		P_1.Background = new ImmutableSolidColorBrush(16777215u);
		P_1.CanResize = false;
		P_1.Title = "赞助支持";
		((ResourceDictionary)P_1.Resources).AddDeferred((object)"CloseIcon", XamlIlRuntimeHelpers.DeferredTransformationFactoryV3<object>((nint)(delegate*<IServiceProvider, object>)(&XamlClosure_1.Build_1), context));
		Styles styles = P_1.Styles;
		Style style;
		Style item = (style = new Style());
		context.PushParent(style);
		Style style2 = style;
		style2.Selector = ((Selector?)null).OfType(typeof(Button)).Class("CardBtn");
		Setter setter = new Setter();
		setter.Property = InputElement.CursorProperty;
		setter.Value = new Cursor(StandardCursorType.Hand);
		style2.Add(setter);
		Setter setter2 = new Setter();
		setter2.Property = TemplatedControl.BorderThicknessProperty;
		setter2.Value = new Thickness(0.0, 0.0, 0.0, 0.0);
		style2.Add(setter2);
		Setter setter4;
		Setter setter3 = (setter4 = new Setter());
		context.PushParent(setter4);
		Setter setter5 = setter4;
		setter5.Property = Visual.RenderTransformProperty;
		setter5.Value = (ITransform)new TransformConverter().ConvertFrom(context, CultureInfo.InvariantCulture, "none");
		context.PopParent();
		style2.Add(setter3);
		Setter setter6 = new Setter();
		setter6.Property = Animatable.TransitionsProperty;
		Transitions transitions = new Transitions();
		TransformOperationsTransition transformOperationsTransition = new TransformOperationsTransition();
		transformOperationsTransition.Property = Visual.RenderTransformProperty;
		transformOperationsTransition.Duration = TimeSpan.FromTicks(2000000L);
		transitions.Add(transformOperationsTransition);
		setter6.Value = transitions;
		style2.Add(setter6);
		Setter setter7 = new Setter();
		setter7.Property = TemplatedControl.TemplateProperty;
		setter7.Value = new ControlTemplate
		{
			Content = XamlIlRuntimeHelpers.DeferredTransformationFactoryV3<Control>((nint)(delegate*<IServiceProvider, object>)(&XamlClosure_1.Build_2), context)
		};
		style2.Add(setter7);
		context.PopParent();
		styles.Add(item);
		Styles styles2 = P_1.Styles;
		Style item2 = (style = new Style());
		context.PushParent(style);
		Style style3 = style;
		style3.Selector = ((Selector?)null).OfType(typeof(Button)).Class("CardBtn").Class(":pointerover");
		Setter setter8 = (setter4 = new Setter());
		context.PushParent(setter4);
		Setter setter9 = setter4;
		setter9.Property = Visual.RenderTransformProperty;
		setter9.Value = (ITransform)new TransformConverter().ConvertFrom(context, CultureInfo.InvariantCulture, "scale(1.02)");
		context.PopParent();
		style3.Add(setter8);
		context.PopParent();
		styles2.Add(item2);
		Styles styles3 = P_1.Styles;
		Style item3 = (style = new Style());
		context.PushParent(style);
		Style style4 = style;
		style4.Selector = ((Selector?)null).OfType(typeof(Button)).Class("CardBtn").Class(":pressed");
		Setter setter10 = (setter4 = new Setter());
		context.PushParent(setter4);
		Setter setter11 = setter4;
		setter11.Property = Visual.RenderTransformProperty;
		setter11.Value = (ITransform)new TransformConverter().ConvertFrom(context, CultureInfo.InvariantCulture, "scale(0.98)");
		context.PopParent();
		style4.Add(setter10);
		context.PopParent();
		styles3.Add(item3);
		Styles styles4 = P_1.Styles;
		Style style5 = new Style();
		style5.Selector = ((Selector?)null).OfType(typeof(Border)).Class("FeatureTag");
		Setter setter12 = new Setter();
		setter12.Property = Border.BackgroundProperty;
		setter12.Value = new ImmutableSolidColorBrush(4286336511u);
		style5.Add(setter12);
		Setter setter13 = new Setter();
		setter13.Property = Border.CornerRadiusProperty;
		setter13.Value = new CornerRadius(12.0, 12.0, 12.0, 12.0);
		style5.Add(setter13);
		Setter setter14 = new Setter();
		setter14.Property = Decorator.PaddingProperty;
		setter14.Value = new Thickness(12.0, 6.0, 12.0, 6.0);
		style5.Add(setter14);
		Setter setter15 = new Setter();
		setter15.Property = Layoutable.MarginProperty;
		setter15.Value = new Thickness(0.0, 5.0, 0.0, 5.0);
		style5.Add(setter15);
		Setter setter16 = new Setter();
		setter16.Property = Border.BoxShadowProperty;
		setter16.Value = BoxShadows.Parse("0 2 5 0 #40000000");
		style5.Add(setter16);
		styles4.Add(style5);
		Border border2;
		Border border = (border2 = new Border());
		((ISupportInitialize)border).BeginInit();
		P_1.Content = border;
		Border border4;
		Border border3 = (border4 = border2);
		context.PushParent(border4);
		Border border5 = border4;
		border5.CornerRadius = new CornerRadius(8.0, 8.0, 8.0, 8.0);
		border5.BoxShadow = BoxShadows.Parse("0 10 30 0 #40000000");
		border5.AddHandler(InputElement.PointerPressedEvent, context.RootObject.Border_PointerPressed);
		LinearGradientBrush linearGradientBrush = new LinearGradientBrush();
		linearGradientBrush.StartPoint = new RelativePoint(0.0, 0.0, RelativeUnit.Relative);
		linearGradientBrush.EndPoint = new RelativePoint(1.0, 1.0, RelativeUnit.Relative);
		GradientStops gradientStops = linearGradientBrush.GradientStops;
		GradientStop gradientStop = new GradientStop();
		gradientStop.Color = Color.FromUInt32(4294507263u);
		gradientStop.Offset = 0.0;
		gradientStops.Add(gradientStop);
		GradientStops gradientStops2 = linearGradientBrush.GradientStops;
		GradientStop gradientStop2 = new GradientStop();
		gradientStop2.Color = Color.FromUInt32(4293456639u);
		gradientStop2.Offset = 1.0;
		gradientStops2.Add(gradientStop2);
		border5.Background = linearGradientBrush;
		Grid grid2;
		Grid grid = (grid2 = new Grid());
		((ISupportInitialize)grid).BeginInit();
		border5.Child = grid;
		Grid grid4;
		Grid grid3 = (grid4 = grid2);
		context.PushParent(grid4);
		Grid grid5 = grid4;
		Controls children = grid5.Children;
		Button button2;
		Button button = (button2 = new Button());
		((ISupportInitialize)button).BeginInit();
		children.Add(button);
		Button button4;
		Button button3 = (button4 = button2);
		context.PushParent(button4);
		Button button5 = button4;
		button5.HorizontalAlignment = HorizontalAlignment.Right;
		button5.VerticalAlignment = VerticalAlignment.Top;
		button5.Margin = new Thickness(0.0, 10.0, 10.0, 0.0);
		button5.AddHandler((RoutedEvent)Button.ClickEvent, (Delegate)new EventHandler<RoutedEventArgs>(context.RootObject.CloseButton_Click), RoutingStrategies.Direct | RoutingStrategies.Bubble, handledEventsToo: false);
		button5.Background = new ImmutableSolidColorBrush(536870912u);
		button5.BorderThickness = new Thickness(0.0, 0.0, 0.0, 0.0);
		button5.Padding = new Thickness(6.0, 6.0, 6.0, 6.0);
		button5.CornerRadius = new CornerRadius(15.0, 15.0, 15.0, 15.0);
		button5.ZIndex = 1;
		PathIcon pathIcon2;
		PathIcon pathIcon = (pathIcon2 = new PathIcon());
		((ISupportInitialize)pathIcon).BeginInit();
		button5.Content = pathIcon;
		PathIcon pathIcon4;
		PathIcon pathIcon3 = (pathIcon4 = pathIcon2);
		context.PushParent(pathIcon4);
		StaticResourceExtension staticResourceExtension = new StaticResourceExtension("CloseIcon");
		context.ProvideTargetProperty = PathIcon.DataProperty;
		object? obj = staticResourceExtension.ProvideValue(context);
		context.ProvideTargetProperty = null;
		CompiledAvaloniaXaml.XamlDynamicSetters._003C_003EXamlDynamicSetter_2(pathIcon4, obj);
		pathIcon4.Height = 10.0;
		pathIcon4.Width = 10.0;
		pathIcon4.Foreground = new ImmutableSolidColorBrush(4278190080u);
		context.PopParent();
		((ISupportInitialize)pathIcon3).EndInit();
		context.PopParent();
		((ISupportInitialize)button3).EndInit();
		Controls children2 = grid5.Children;
		StackPanel stackPanel2;
		StackPanel stackPanel = (stackPanel2 = new StackPanel());
		((ISupportInitialize)stackPanel).BeginInit();
		children2.Add(stackPanel);
		StackPanel stackPanel4;
		StackPanel stackPanel3 = (stackPanel4 = stackPanel2);
		context.PushParent(stackPanel4);
		StackPanel stackPanel5 = stackPanel4;
		stackPanel5.VerticalAlignment = VerticalAlignment.Center;
		stackPanel5.Margin = new Thickness(0.0, 40.0, 0.0, 25.0);
		Controls children3 = stackPanel5.Children;
		TextBlock textBlock2;
		TextBlock textBlock = (textBlock2 = new TextBlock());
		((ISupportInitialize)textBlock).BeginInit();
		children3.Add(textBlock);
		textBlock2.Text = "Cursor无限额度，告别额度焦虑";
		textBlock2.FontSize = 17.0;
		textBlock2.FontWeight = FontWeight.Bold;
		textBlock2.Foreground = new ImmutableSolidColorBrush(4283076834u);
		textBlock2.HorizontalAlignment = HorizontalAlignment.Center;
		textBlock2.Margin = new Thickness(0.0, 20.0, 0.0, 0.0);
		((ISupportInitialize)textBlock2).EndInit();
		Controls children4 = stackPanel5.Children;
		Grid grid7;
		Grid grid6 = (grid7 = new Grid());
		((ISupportInitialize)grid6).BeginInit();
		children4.Add(grid6);
		Grid grid8 = (grid4 = grid7);
		context.PushParent(grid4);
		Grid grid9 = grid4;
		grid9.HorizontalAlignment = HorizontalAlignment.Center;
		grid9.Margin = new Thickness(0.0, 40.0, 0.0, 20.0);
		grid9.ColumnDefinitions.Add(new ColumnDefinition
		{
			Width = new GridLength(0.0, GridUnitType.Auto)
		});
		grid9.ColumnDefinitions.Add(new ColumnDefinition
		{
			Width = new GridLength(20.0, GridUnitType.Pixel)
		});
		grid9.ColumnDefinitions.Add(new ColumnDefinition
		{
			Width = new GridLength(0.0, GridUnitType.Auto)
		});
		Controls children5 = grid9.Children;
		Button button7;
		Button button6 = (button7 = new Button());
		((ISupportInitialize)button6).BeginInit();
		children5.Add(button6);
		Button button8 = (button4 = button7);
		context.PushParent(button4);
		Button button9 = button4;
		Grid.SetColumn(button9, 0);
		button9.Width = 150.0;
		button9.Height = 135.0;
		StyledProperty<ICommand?> commandProperty = Button.CommandProperty;
		CompiledBindingExtension compiledBindingExtension = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E选择卡密Command_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = Button.CommandProperty;
		CompiledBindingExtension binding = compiledBindingExtension.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(button9, commandProperty, (IBinding)binding);
		button9.CommandParameter = "周卡";
		button9.Classes.Add("CardBtn");
		Grid grid11;
		Grid grid10 = (grid11 = new Grid());
		((ISupportInitialize)grid10).BeginInit();
		button9.Content = grid10;
		Grid grid12 = (grid4 = grid11);
		context.PushParent(grid4);
		Grid grid13 = grid4;
		grid13.Width = 150.0;
		grid13.Height = 135.0;
		Controls children6 = grid13.Children;
		Border border7;
		Border border6 = (border7 = new Border());
		((ISupportInitialize)border6).BeginInit();
		children6.Add(border6);
		Border border8 = (border4 = border7);
		context.PushParent(border4);
		Border border9 = border4;
		border9.Background = new ImmutableSolidColorBrush(4294929205u);
		border9.CornerRadius = new CornerRadius(8.0, 8.0, 8.0, 8.0);
		border9.Padding = new Thickness(6.0, 2.0, 6.0, 2.0);
		border9.HorizontalAlignment = HorizontalAlignment.Right;
		border9.VerticalAlignment = VerticalAlignment.Top;
		border9.Margin = new Thickness(0.0, 2.0, 2.0, 0.0);
		StyledProperty<bool> isVisibleProperty = Visual.IsVisibleProperty;
		CompiledBindingExtension compiledBindingExtension2 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡有特惠_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = Visual.IsVisibleProperty;
		CompiledBindingExtension binding2 = compiledBindingExtension2.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(border9, isVisibleProperty, (IBinding)binding2);
		TextBlock textBlock4;
		TextBlock textBlock3 = (textBlock4 = new TextBlock());
		((ISupportInitialize)textBlock3).BeginInit();
		border9.Child = textBlock3;
		textBlock4.Text = "今日特惠";
		textBlock4.FontSize = 10.0;
		textBlock4.FontWeight = FontWeight.Bold;
		textBlock4.Foreground = new ImmutableSolidColorBrush(uint.MaxValue);
		textBlock4.HorizontalAlignment = HorizontalAlignment.Center;
		((ISupportInitialize)textBlock4).EndInit();
		context.PopParent();
		((ISupportInitialize)border8).EndInit();
		Controls children7 = grid13.Children;
		StackPanel stackPanel7;
		StackPanel stackPanel6 = (stackPanel7 = new StackPanel());
		((ISupportInitialize)stackPanel6).BeginInit();
		children7.Add(stackPanel6);
		StackPanel stackPanel8 = (stackPanel4 = stackPanel7);
		context.PushParent(stackPanel4);
		StackPanel stackPanel9 = stackPanel4;
		stackPanel9.VerticalAlignment = VerticalAlignment.Center;
		stackPanel9.HorizontalAlignment = HorizontalAlignment.Center;
		Controls children8 = stackPanel9.Children;
		StackPanel stackPanel11;
		StackPanel stackPanel10 = (stackPanel11 = new StackPanel());
		((ISupportInitialize)stackPanel10).BeginInit();
		children8.Add(stackPanel10);
		stackPanel11.Orientation = Orientation.Horizontal;
		stackPanel11.HorizontalAlignment = HorizontalAlignment.Center;
		stackPanel11.Margin = new Thickness(0.0, 0.0, 0.0, 4.0);
		Controls children9 = stackPanel11.Children;
		TextBlock textBlock6;
		TextBlock textBlock5 = (textBlock6 = new TextBlock());
		((ISupportInitialize)textBlock5).BeginInit();
		children9.Add(textBlock5);
		textBlock6.Text = "7天";
		textBlock6.FontSize = 19.0;
		textBlock6.FontWeight = FontWeight.Bold;
		textBlock6.Foreground = new ImmutableSolidColorBrush(uint.MaxValue);
		textBlock6.Margin = new Thickness(0.0, 0.0, 6.0, 0.0);
		((ISupportInitialize)textBlock6).EndInit();
		Controls children10 = stackPanel11.Children;
		TextBlock textBlock8;
		TextBlock textBlock7 = (textBlock8 = new TextBlock());
		((ISupportInitialize)textBlock7).BeginInit();
		children10.Add(textBlock7);
		textBlock8.Text = "独享";
		textBlock8.FontSize = 15.0;
		textBlock8.FontWeight = FontWeight.Bold;
		textBlock8.Foreground = new ImmutableSolidColorBrush(4293968203u);
		textBlock8.VerticalAlignment = VerticalAlignment.Center;
		textBlock8.Margin = new Thickness(0.0, 3.0, 0.0, 0.0);
		((ISupportInitialize)textBlock8).EndInit();
		((ISupportInitialize)stackPanel11).EndInit();
		Controls children11 = stackPanel9.Children;
		TextBlock textBlock10;
		TextBlock textBlock9 = (textBlock10 = new TextBlock());
		((ISupportInitialize)textBlock9).BeginInit();
		children11.Add(textBlock9);
		textBlock10.Text = "快速额度：无限";
		textBlock10.FontSize = 15.0;
		textBlock10.FontWeight = FontWeight.Bold;
		textBlock10.Foreground = new ImmutableSolidColorBrush(4294956800u);
		textBlock10.HorizontalAlignment = HorizontalAlignment.Center;
		textBlock10.Margin = new Thickness(0.0, 0.0, 0.0, 6.0);
		((ISupportInitialize)textBlock10).EndInit();
		Controls children12 = stackPanel9.Children;
		StackPanel stackPanel13;
		StackPanel stackPanel12 = (stackPanel13 = new StackPanel());
		((ISupportInitialize)stackPanel12).BeginInit();
		children12.Add(stackPanel12);
		StackPanel stackPanel14 = (stackPanel4 = stackPanel13);
		context.PushParent(stackPanel4);
		StackPanel stackPanel15 = stackPanel4;
		stackPanel15.HorizontalAlignment = HorizontalAlignment.Center;
		Controls children13 = stackPanel15.Children;
		StackPanel stackPanel17;
		StackPanel stackPanel16 = (stackPanel17 = new StackPanel());
		((ISupportInitialize)stackPanel16).BeginInit();
		children13.Add(stackPanel16);
		StackPanel stackPanel18 = (stackPanel4 = stackPanel17);
		context.PushParent(stackPanel4);
		StackPanel stackPanel19 = stackPanel4;
		stackPanel19.Orientation = Orientation.Horizontal;
		stackPanel19.HorizontalAlignment = HorizontalAlignment.Center;
		StyledProperty<bool> isVisibleProperty2 = Visual.IsVisibleProperty;
		CompiledBindingExtension compiledBindingExtension3 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡有特惠_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = Visual.IsVisibleProperty;
		CompiledBindingExtension binding3 = compiledBindingExtension3.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(stackPanel19, isVisibleProperty2, (IBinding)binding3);
		Controls children14 = stackPanel19.Children;
		TextBlock textBlock12;
		TextBlock textBlock11 = (textBlock12 = new TextBlock());
		((ISupportInitialize)textBlock11).BeginInit();
		children14.Add(textBlock11);
		textBlock12.Text = "￥";
		textBlock12.FontSize = 12.0;
		textBlock12.Foreground = new ImmutableSolidColorBrush(4291611852u);
		textBlock12.VerticalAlignment = VerticalAlignment.Bottom;
		textBlock12.Margin = new Thickness(0.0, 0.0, 1.0, 1.0);
		((ISupportInitialize)textBlock12).EndInit();
		Controls children15 = stackPanel19.Children;
		TextBlock textBlock14;
		TextBlock textBlock13 = (textBlock14 = new TextBlock());
		((ISupportInitialize)textBlock13).BeginInit();
		children15.Add(textBlock13);
		TextBlock textBlock16;
		TextBlock textBlock15 = (textBlock16 = textBlock14);
		context.PushParent(textBlock16);
		TextBlock textBlock17 = textBlock16;
		StyledProperty<string?> textProperty = TextBlock.TextProperty;
		CompiledBindingExtension compiledBindingExtension4 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡原价_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = TextBlock.TextProperty;
		CompiledBindingExtension binding4 = compiledBindingExtension4.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(textBlock17, textProperty, (IBinding)binding4);
		textBlock17.FontSize = 12.0;
		textBlock17.Foreground = new ImmutableSolidColorBrush(4291611852u);
		textBlock17.TextDecorations = TextDecorations.Strikethrough;
		context.PopParent();
		((ISupportInitialize)textBlock15).EndInit();
		context.PopParent();
		((ISupportInitialize)stackPanel18).EndInit();
		Controls children16 = stackPanel15.Children;
		StackPanel stackPanel21;
		StackPanel stackPanel20 = (stackPanel21 = new StackPanel());
		((ISupportInitialize)stackPanel20).BeginInit();
		children16.Add(stackPanel20);
		StackPanel stackPanel22 = (stackPanel4 = stackPanel21);
		context.PushParent(stackPanel4);
		StackPanel stackPanel23 = stackPanel4;
		stackPanel23.Orientation = Orientation.Horizontal;
		stackPanel23.HorizontalAlignment = HorizontalAlignment.Center;
		Controls children17 = stackPanel23.Children;
		TextBlock textBlock19;
		TextBlock textBlock18 = (textBlock19 = new TextBlock());
		((ISupportInitialize)textBlock18).BeginInit();
		children17.Add(textBlock18);
		textBlock19.Text = "￥";
		textBlock19.FontSize = 15.0;
		textBlock19.Foreground = new ImmutableSolidColorBrush(uint.MaxValue);
		textBlock19.FontWeight = FontWeight.Bold;
		textBlock19.VerticalAlignment = VerticalAlignment.Bottom;
		textBlock19.Margin = new Thickness(0.0, 0.0, 2.0, 1.0);
		((ISupportInitialize)textBlock19).EndInit();
		Controls children18 = stackPanel23.Children;
		TextBlock textBlock21;
		TextBlock textBlock20 = (textBlock21 = new TextBlock());
		((ISupportInitialize)textBlock20).BeginInit();
		children18.Add(textBlock20);
		TextBlock textBlock22 = (textBlock16 = textBlock21);
		context.PushParent(textBlock16);
		TextBlock textBlock23 = textBlock16;
		StyledProperty<string?> textProperty2 = TextBlock.TextProperty;
		CompiledBindingExtension compiledBindingExtension5 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡现价_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = TextBlock.TextProperty;
		CompiledBindingExtension binding5 = compiledBindingExtension5.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(textBlock23, textProperty2, (IBinding)binding5);
		textBlock23.FontSize = 15.0;
		textBlock23.FontWeight = FontWeight.Bold;
		textBlock23.Foreground = new ImmutableSolidColorBrush(uint.MaxValue);
		StyledProperty<bool> isVisibleProperty3 = Visual.IsVisibleProperty;
		CompiledBindingExtension compiledBindingExtension6 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Not().Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E价格加载中_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = Visual.IsVisibleProperty;
		CompiledBindingExtension binding6 = compiledBindingExtension6.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(textBlock23, isVisibleProperty3, (IBinding)binding6);
		context.PopParent();
		((ISupportInitialize)textBlock22).EndInit();
		Controls children19 = stackPanel23.Children;
		TextBlock textBlock25;
		TextBlock textBlock24 = (textBlock25 = new TextBlock());
		((ISupportInitialize)textBlock24).BeginInit();
		children19.Add(textBlock24);
		TextBlock textBlock26 = (textBlock16 = textBlock25);
		context.PushParent(textBlock16);
		TextBlock textBlock27 = textBlock16;
		textBlock27.Text = "...";
		textBlock27.FontSize = 15.0;
		textBlock27.FontWeight = FontWeight.Bold;
		textBlock27.Foreground = new ImmutableSolidColorBrush(uint.MaxValue);
		StyledProperty<bool> isVisibleProperty4 = Visual.IsVisibleProperty;
		CompiledBindingExtension compiledBindingExtension7 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E价格加载中_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = Visual.IsVisibleProperty;
		CompiledBindingExtension binding7 = compiledBindingExtension7.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(textBlock27, isVisibleProperty4, (IBinding)binding7);
		context.PopParent();
		((ISupportInitialize)textBlock26).EndInit();
		context.PopParent();
		((ISupportInitialize)stackPanel22).EndInit();
		context.PopParent();
		((ISupportInitialize)stackPanel14).EndInit();
		context.PopParent();
		((ISupportInitialize)stackPanel8).EndInit();
		context.PopParent();
		((ISupportInitialize)grid12).EndInit();
		context.PopParent();
		((ISupportInitialize)button8).EndInit();
		Controls children20 = grid9.Children;
		Button button11;
		Button button10 = (button11 = new Button());
		((ISupportInitialize)button10).BeginInit();
		children20.Add(button10);
		Button button12 = (button4 = button11);
		context.PushParent(button4);
		Button button13 = button4;
		Grid.SetColumn(button13, 2);
		button13.Width = 150.0;
		button13.Height = 135.0;
		StyledProperty<ICommand?> commandProperty2 = Button.CommandProperty;
		CompiledBindingExtension compiledBindingExtension8 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E选择卡密Command_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = Button.CommandProperty;
		CompiledBindingExtension binding8 = compiledBindingExtension8.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(button13, commandProperty2, (IBinding)binding8);
		button13.CommandParameter = "月卡";
		button13.Classes.Add("CardBtn");
		Grid grid15;
		Grid grid14 = (grid15 = new Grid());
		((ISupportInitialize)grid14).BeginInit();
		button13.Content = grid14;
		Grid grid16 = (grid4 = grid15);
		context.PushParent(grid4);
		Grid grid17 = grid4;
		grid17.Width = 150.0;
		grid17.Height = 135.0;
		Controls children21 = grid17.Children;
		Border border11;
		Border border10 = (border11 = new Border());
		((ISupportInitialize)border10).BeginInit();
		children21.Add(border10);
		Border border12 = (border4 = border11);
		context.PushParent(border4);
		Border border13 = border4;
		border13.Background = new ImmutableSolidColorBrush(4294929205u);
		border13.CornerRadius = new CornerRadius(8.0, 8.0, 8.0, 8.0);
		border13.Padding = new Thickness(6.0, 2.0, 6.0, 2.0);
		border13.HorizontalAlignment = HorizontalAlignment.Right;
		border13.VerticalAlignment = VerticalAlignment.Top;
		border13.Margin = new Thickness(0.0, 2.0, 2.0, 0.0);
		StyledProperty<bool> isVisibleProperty5 = Visual.IsVisibleProperty;
		CompiledBindingExtension compiledBindingExtension9 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡有特惠_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = Visual.IsVisibleProperty;
		CompiledBindingExtension binding9 = compiledBindingExtension9.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(border13, isVisibleProperty5, (IBinding)binding9);
		TextBlock textBlock29;
		TextBlock textBlock28 = (textBlock29 = new TextBlock());
		((ISupportInitialize)textBlock28).BeginInit();
		border13.Child = textBlock28;
		textBlock29.Text = "今日特惠";
		textBlock29.FontSize = 10.0;
		textBlock29.FontWeight = FontWeight.Bold;
		textBlock29.Foreground = new ImmutableSolidColorBrush(uint.MaxValue);
		textBlock29.HorizontalAlignment = HorizontalAlignment.Center;
		((ISupportInitialize)textBlock29).EndInit();
		context.PopParent();
		((ISupportInitialize)border12).EndInit();
		Controls children22 = grid17.Children;
		StackPanel stackPanel25;
		StackPanel stackPanel24 = (stackPanel25 = new StackPanel());
		((ISupportInitialize)stackPanel24).BeginInit();
		children22.Add(stackPanel24);
		StackPanel stackPanel26 = (stackPanel4 = stackPanel25);
		context.PushParent(stackPanel4);
		StackPanel stackPanel27 = stackPanel4;
		stackPanel27.VerticalAlignment = VerticalAlignment.Center;
		stackPanel27.HorizontalAlignment = HorizontalAlignment.Center;
		Controls children23 = stackPanel27.Children;
		StackPanel stackPanel29;
		StackPanel stackPanel28 = (stackPanel29 = new StackPanel());
		((ISupportInitialize)stackPanel28).BeginInit();
		children23.Add(stackPanel28);
		stackPanel29.Orientation = Orientation.Horizontal;
		stackPanel29.HorizontalAlignment = HorizontalAlignment.Center;
		stackPanel29.Margin = new Thickness(0.0, 0.0, 0.0, 4.0);
		Controls children24 = stackPanel29.Children;
		TextBlock textBlock31;
		TextBlock textBlock30 = (textBlock31 = new TextBlock());
		((ISupportInitialize)textBlock30).BeginInit();
		children24.Add(textBlock30);
		textBlock31.Text = "30天";
		textBlock31.FontSize = 19.0;
		textBlock31.FontWeight = FontWeight.Bold;
		textBlock31.Foreground = new ImmutableSolidColorBrush(uint.MaxValue);
		textBlock31.Margin = new Thickness(0.0, 0.0, 6.0, 0.0);
		((ISupportInitialize)textBlock31).EndInit();
		Controls children25 = stackPanel29.Children;
		TextBlock textBlock33;
		TextBlock textBlock32 = (textBlock33 = new TextBlock());
		((ISupportInitialize)textBlock32).BeginInit();
		children25.Add(textBlock32);
		textBlock33.Text = "独享";
		textBlock33.FontSize = 15.0;
		textBlock33.FontWeight = FontWeight.Bold;
		textBlock33.Foreground = new ImmutableSolidColorBrush(4293968203u);
		textBlock33.VerticalAlignment = VerticalAlignment.Center;
		textBlock33.Margin = new Thickness(0.0, 3.0, 0.0, 0.0);
		((ISupportInitialize)textBlock33).EndInit();
		((ISupportInitialize)stackPanel29).EndInit();
		Controls children26 = stackPanel27.Children;
		TextBlock textBlock35;
		TextBlock textBlock34 = (textBlock35 = new TextBlock());
		((ISupportInitialize)textBlock34).BeginInit();
		children26.Add(textBlock34);
		textBlock35.Text = "快速额度：无限";
		textBlock35.FontSize = 15.0;
		textBlock35.FontWeight = FontWeight.Bold;
		textBlock35.Foreground = new ImmutableSolidColorBrush(4294956800u);
		textBlock35.HorizontalAlignment = HorizontalAlignment.Center;
		textBlock35.Margin = new Thickness(0.0, 0.0, 0.0, 6.0);
		((ISupportInitialize)textBlock35).EndInit();
		Controls children27 = stackPanel27.Children;
		StackPanel stackPanel31;
		StackPanel stackPanel30 = (stackPanel31 = new StackPanel());
		((ISupportInitialize)stackPanel30).BeginInit();
		children27.Add(stackPanel30);
		StackPanel stackPanel32 = (stackPanel4 = stackPanel31);
		context.PushParent(stackPanel4);
		StackPanel stackPanel33 = stackPanel4;
		stackPanel33.HorizontalAlignment = HorizontalAlignment.Center;
		Controls children28 = stackPanel33.Children;
		StackPanel stackPanel35;
		StackPanel stackPanel34 = (stackPanel35 = new StackPanel());
		((ISupportInitialize)stackPanel34).BeginInit();
		children28.Add(stackPanel34);
		StackPanel stackPanel36 = (stackPanel4 = stackPanel35);
		context.PushParent(stackPanel4);
		StackPanel stackPanel37 = stackPanel4;
		stackPanel37.Orientation = Orientation.Horizontal;
		stackPanel37.HorizontalAlignment = HorizontalAlignment.Center;
		StyledProperty<bool> isVisibleProperty6 = Visual.IsVisibleProperty;
		CompiledBindingExtension compiledBindingExtension10 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡有特惠_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = Visual.IsVisibleProperty;
		CompiledBindingExtension binding10 = compiledBindingExtension10.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(stackPanel37, isVisibleProperty6, (IBinding)binding10);
		Controls children29 = stackPanel37.Children;
		TextBlock textBlock37;
		TextBlock textBlock36 = (textBlock37 = new TextBlock());
		((ISupportInitialize)textBlock36).BeginInit();
		children29.Add(textBlock36);
		textBlock37.Text = "￥";
		textBlock37.FontSize = 12.0;
		textBlock37.Foreground = new ImmutableSolidColorBrush(4291611852u);
		textBlock37.VerticalAlignment = VerticalAlignment.Bottom;
		textBlock37.Margin = new Thickness(0.0, 0.0, 1.0, 1.0);
		((ISupportInitialize)textBlock37).EndInit();
		Controls children30 = stackPanel37.Children;
		TextBlock textBlock39;
		TextBlock textBlock38 = (textBlock39 = new TextBlock());
		((ISupportInitialize)textBlock38).BeginInit();
		children30.Add(textBlock38);
		TextBlock textBlock40 = (textBlock16 = textBlock39);
		context.PushParent(textBlock16);
		TextBlock textBlock41 = textBlock16;
		StyledProperty<string?> textProperty3 = TextBlock.TextProperty;
		CompiledBindingExtension compiledBindingExtension11 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡原价_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = TextBlock.TextProperty;
		CompiledBindingExtension binding11 = compiledBindingExtension11.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(textBlock41, textProperty3, (IBinding)binding11);
		textBlock41.FontSize = 12.0;
		textBlock41.Foreground = new ImmutableSolidColorBrush(4291611852u);
		textBlock41.TextDecorations = TextDecorations.Strikethrough;
		context.PopParent();
		((ISupportInitialize)textBlock40).EndInit();
		context.PopParent();
		((ISupportInitialize)stackPanel36).EndInit();
		Controls children31 = stackPanel33.Children;
		StackPanel stackPanel39;
		StackPanel stackPanel38 = (stackPanel39 = new StackPanel());
		((ISupportInitialize)stackPanel38).BeginInit();
		children31.Add(stackPanel38);
		StackPanel stackPanel40 = (stackPanel4 = stackPanel39);
		context.PushParent(stackPanel4);
		StackPanel stackPanel41 = stackPanel4;
		stackPanel41.Orientation = Orientation.Horizontal;
		stackPanel41.HorizontalAlignment = HorizontalAlignment.Center;
		Controls children32 = stackPanel41.Children;
		TextBlock textBlock43;
		TextBlock textBlock42 = (textBlock43 = new TextBlock());
		((ISupportInitialize)textBlock42).BeginInit();
		children32.Add(textBlock42);
		textBlock43.Text = "￥";
		textBlock43.FontSize = 15.0;
		textBlock43.Foreground = new ImmutableSolidColorBrush(uint.MaxValue);
		textBlock43.FontWeight = FontWeight.Bold;
		textBlock43.VerticalAlignment = VerticalAlignment.Bottom;
		textBlock43.Margin = new Thickness(0.0, 0.0, 2.0, 1.0);
		((ISupportInitialize)textBlock43).EndInit();
		Controls children33 = stackPanel41.Children;
		TextBlock textBlock45;
		TextBlock textBlock44 = (textBlock45 = new TextBlock());
		((ISupportInitialize)textBlock44).BeginInit();
		children33.Add(textBlock44);
		TextBlock textBlock46 = (textBlock16 = textBlock45);
		context.PushParent(textBlock16);
		TextBlock textBlock47 = textBlock16;
		StyledProperty<string?> textProperty4 = TextBlock.TextProperty;
		CompiledBindingExtension compiledBindingExtension12 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡现价_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = TextBlock.TextProperty;
		CompiledBindingExtension binding12 = compiledBindingExtension12.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(textBlock47, textProperty4, (IBinding)binding12);
		textBlock47.FontSize = 15.0;
		textBlock47.FontWeight = FontWeight.Bold;
		textBlock47.Foreground = new ImmutableSolidColorBrush(uint.MaxValue);
		StyledProperty<bool> isVisibleProperty7 = Visual.IsVisibleProperty;
		CompiledBindingExtension compiledBindingExtension13 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Not().Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E价格加载中_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = Visual.IsVisibleProperty;
		CompiledBindingExtension binding13 = compiledBindingExtension13.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(textBlock47, isVisibleProperty7, (IBinding)binding13);
		context.PopParent();
		((ISupportInitialize)textBlock46).EndInit();
		Controls children34 = stackPanel41.Children;
		TextBlock textBlock49;
		TextBlock textBlock48 = (textBlock49 = new TextBlock());
		((ISupportInitialize)textBlock48).BeginInit();
		children34.Add(textBlock48);
		TextBlock textBlock50 = (textBlock16 = textBlock49);
		context.PushParent(textBlock16);
		TextBlock textBlock51 = textBlock16;
		textBlock51.Text = "...";
		textBlock51.FontSize = 15.0;
		textBlock51.FontWeight = FontWeight.Bold;
		textBlock51.Foreground = new ImmutableSolidColorBrush(uint.MaxValue);
		StyledProperty<bool> isVisibleProperty8 = Visual.IsVisibleProperty;
		CompiledBindingExtension compiledBindingExtension14 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E价格加载中_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = Visual.IsVisibleProperty;
		CompiledBindingExtension binding14 = compiledBindingExtension14.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(textBlock51, isVisibleProperty8, (IBinding)binding14);
		context.PopParent();
		((ISupportInitialize)textBlock50).EndInit();
		context.PopParent();
		((ISupportInitialize)stackPanel40).EndInit();
		context.PopParent();
		((ISupportInitialize)stackPanel32).EndInit();
		context.PopParent();
		((ISupportInitialize)stackPanel26).EndInit();
		context.PopParent();
		((ISupportInitialize)grid16).EndInit();
		context.PopParent();
		((ISupportInitialize)button12).EndInit();
		context.PopParent();
		((ISupportInitialize)grid8).EndInit();
		Controls children35 = stackPanel5.Children;
		Grid grid19;
		Grid grid18 = (grid19 = new Grid());
		((ISupportInitialize)grid18).BeginInit();
		children35.Add(grid18);
		Grid grid20 = (grid4 = grid19);
		context.PushParent(grid4);
		Grid grid21 = grid4;
		grid21.HorizontalAlignment = HorizontalAlignment.Center;
		grid21.Margin = new Thickness(0.0, -15.0, 0.0, 20.0);
		grid21.ColumnDefinitions.Add(new ColumnDefinition
		{
			Width = new GridLength(150.0, GridUnitType.Pixel)
		});
		grid21.ColumnDefinitions.Add(new ColumnDefinition
		{
			Width = new GridLength(20.0, GridUnitType.Pixel)
		});
		grid21.ColumnDefinitions.Add(new ColumnDefinition
		{
			Width = new GridLength(150.0, GridUnitType.Pixel)
		});
		Controls children36 = grid21.Children;
		StackPanel stackPanel43;
		StackPanel stackPanel42 = (stackPanel43 = new StackPanel());
		((ISupportInitialize)stackPanel42).BeginInit();
		children36.Add(stackPanel42);
		StackPanel stackPanel44 = (stackPanel4 = stackPanel43);
		context.PushParent(stackPanel4);
		StackPanel stackPanel45 = stackPanel4;
		Grid.SetColumn(stackPanel45, 0);
		stackPanel45.HorizontalAlignment = HorizontalAlignment.Center;
		Controls children37 = stackPanel45.Children;
		StackPanel stackPanel47;
		StackPanel stackPanel46 = (stackPanel47 = new StackPanel());
		((ISupportInitialize)stackPanel46).BeginInit();
		children37.Add(stackPanel46);
		StackPanel stackPanel48 = (stackPanel4 = stackPanel47);
		context.PushParent(stackPanel4);
		StackPanel stackPanel49 = stackPanel4;
		stackPanel49.Orientation = Orientation.Horizontal;
		stackPanel49.HorizontalAlignment = HorizontalAlignment.Center;
		Controls children38 = stackPanel49.Children;
		Button button15;
		Button button14 = (button15 = new Button());
		((ISupportInitialize)button14).BeginInit();
		children38.Add(button14);
		Button button16 = (button4 = button15);
		context.PushParent(button4);
		Button button17 = button4;
		button17.Content = "−";
		button17.Width = 30.0;
		button17.Height = 30.0;
		button17.FontSize = 16.0;
		button17.FontWeight = FontWeight.Bold;
		StyledProperty<ICommand?> commandProperty3 = Button.CommandProperty;
		CompiledBindingExtension compiledBindingExtension15 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E调整周卡数量Command_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = Button.CommandProperty;
		CompiledBindingExtension binding15 = compiledBindingExtension15.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(button17, commandProperty3, (IBinding)binding15);
		button17.CommandParameter = "减少";
		button17.Background = new ImmutableSolidColorBrush(4293454056u);
		button17.Foreground = new ImmutableSolidColorBrush(4284900966u);
		button17.BorderThickness = new Thickness(0.0, 0.0, 0.0, 0.0);
		button17.CornerRadius = new CornerRadius(6.0, 6.0, 6.0, 6.0);
		context.PopParent();
		((ISupportInitialize)button16).EndInit();
		Controls children39 = stackPanel49.Children;
		TextBlock textBlock53;
		TextBlock textBlock52 = (textBlock53 = new TextBlock());
		((ISupportInitialize)textBlock52).BeginInit();
		children39.Add(textBlock52);
		TextBlock textBlock54 = (textBlock16 = textBlock53);
		context.PushParent(textBlock16);
		TextBlock textBlock55 = textBlock16;
		StyledProperty<string?> textProperty5 = TextBlock.TextProperty;
		CompiledBindingExtension compiledBindingExtension16 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡数量_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = TextBlock.TextProperty;
		CompiledBindingExtension binding16 = compiledBindingExtension16.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(textBlock55, textProperty5, (IBinding)binding16);
		textBlock55.Width = 40.0;
		textBlock55.TextAlignment = TextAlignment.Center;
		textBlock55.VerticalAlignment = VerticalAlignment.Center;
		textBlock55.FontSize = 16.0;
		textBlock55.FontWeight = FontWeight.Bold;
		textBlock55.Foreground = new ImmutableSolidColorBrush(4281545523u);
		textBlock55.Margin = new Thickness(10.0, 0.0, 10.0, 0.0);
		context.PopParent();
		((ISupportInitialize)textBlock54).EndInit();
		Controls children40 = stackPanel49.Children;
		Button button19;
		Button button18 = (button19 = new Button());
		((ISupportInitialize)button18).BeginInit();
		children40.Add(button18);
		Button button20 = (button4 = button19);
		context.PushParent(button4);
		Button button21 = button4;
		button21.Content = "+";
		button21.Width = 30.0;
		button21.Height = 30.0;
		button21.FontSize = 14.0;
		button21.FontWeight = FontWeight.Bold;
		StyledProperty<ICommand?> commandProperty4 = Button.CommandProperty;
		CompiledBindingExtension compiledBindingExtension17 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E调整周卡数量Command_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = Button.CommandProperty;
		CompiledBindingExtension binding17 = compiledBindingExtension17.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(button21, commandProperty4, (IBinding)binding17);
		button21.CommandParameter = "增加";
		button21.Background = new ImmutableSolidColorBrush(4293454056u);
		button21.Foreground = new ImmutableSolidColorBrush(4284900966u);
		button21.BorderThickness = new Thickness(0.0, 0.0, 0.0, 0.0);
		button21.CornerRadius = new CornerRadius(6.0, 6.0, 6.0, 6.0);
		context.PopParent();
		((ISupportInitialize)button20).EndInit();
		context.PopParent();
		((ISupportInitialize)stackPanel48).EndInit();
		context.PopParent();
		((ISupportInitialize)stackPanel44).EndInit();
		Controls children41 = grid21.Children;
		StackPanel stackPanel51;
		StackPanel stackPanel50 = (stackPanel51 = new StackPanel());
		((ISupportInitialize)stackPanel50).BeginInit();
		children41.Add(stackPanel50);
		StackPanel stackPanel52 = (stackPanel4 = stackPanel51);
		context.PushParent(stackPanel4);
		StackPanel stackPanel53 = stackPanel4;
		Grid.SetColumn(stackPanel53, 2);
		stackPanel53.HorizontalAlignment = HorizontalAlignment.Center;
		Controls children42 = stackPanel53.Children;
		StackPanel stackPanel55;
		StackPanel stackPanel54 = (stackPanel55 = new StackPanel());
		((ISupportInitialize)stackPanel54).BeginInit();
		children42.Add(stackPanel54);
		StackPanel stackPanel56 = (stackPanel4 = stackPanel55);
		context.PushParent(stackPanel4);
		StackPanel stackPanel57 = stackPanel4;
		stackPanel57.Orientation = Orientation.Horizontal;
		stackPanel57.HorizontalAlignment = HorizontalAlignment.Center;
		Controls children43 = stackPanel57.Children;
		Button button23;
		Button button22 = (button23 = new Button());
		((ISupportInitialize)button22).BeginInit();
		children43.Add(button22);
		Button button24 = (button4 = button23);
		context.PushParent(button4);
		Button button25 = button4;
		button25.Content = "−";
		button25.Width = 30.0;
		button25.Height = 30.0;
		button25.FontSize = 16.0;
		button25.FontWeight = FontWeight.Bold;
		StyledProperty<ICommand?> commandProperty5 = Button.CommandProperty;
		CompiledBindingExtension compiledBindingExtension18 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E调整月卡数量Command_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = Button.CommandProperty;
		CompiledBindingExtension binding18 = compiledBindingExtension18.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(button25, commandProperty5, (IBinding)binding18);
		button25.CommandParameter = "减少";
		button25.Background = new ImmutableSolidColorBrush(4293454056u);
		button25.Foreground = new ImmutableSolidColorBrush(4284900966u);
		button25.BorderThickness = new Thickness(0.0, 0.0, 0.0, 0.0);
		button25.CornerRadius = new CornerRadius(6.0, 6.0, 6.0, 6.0);
		context.PopParent();
		((ISupportInitialize)button24).EndInit();
		Controls children44 = stackPanel57.Children;
		TextBlock textBlock57;
		TextBlock textBlock56 = (textBlock57 = new TextBlock());
		((ISupportInitialize)textBlock56).BeginInit();
		children44.Add(textBlock56);
		TextBlock textBlock58 = (textBlock16 = textBlock57);
		context.PushParent(textBlock16);
		TextBlock textBlock59 = textBlock16;
		StyledProperty<string?> textProperty6 = TextBlock.TextProperty;
		CompiledBindingExtension compiledBindingExtension19 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡数量_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = TextBlock.TextProperty;
		CompiledBindingExtension binding19 = compiledBindingExtension19.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(textBlock59, textProperty6, (IBinding)binding19);
		textBlock59.Width = 40.0;
		textBlock59.TextAlignment = TextAlignment.Center;
		textBlock59.VerticalAlignment = VerticalAlignment.Center;
		textBlock59.FontSize = 16.0;
		textBlock59.FontWeight = FontWeight.Bold;
		textBlock59.Foreground = new ImmutableSolidColorBrush(4281545523u);
		textBlock59.Margin = new Thickness(10.0, 0.0, 10.0, 0.0);
		context.PopParent();
		((ISupportInitialize)textBlock58).EndInit();
		Controls children45 = stackPanel57.Children;
		Button button27;
		Button button26 = (button27 = new Button());
		((ISupportInitialize)button26).BeginInit();
		children45.Add(button26);
		Button button28 = (button4 = button27);
		context.PushParent(button4);
		Button button29 = button4;
		button29.Content = "+";
		button29.Width = 30.0;
		button29.Height = 30.0;
		button29.FontSize = 14.0;
		button29.FontWeight = FontWeight.Bold;
		StyledProperty<ICommand?> commandProperty6 = Button.CommandProperty;
		CompiledBindingExtension compiledBindingExtension20 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E调整月卡数量Command_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = Button.CommandProperty;
		CompiledBindingExtension binding20 = compiledBindingExtension20.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(button29, commandProperty6, (IBinding)binding20);
		button29.CommandParameter = "增加";
		button29.Background = new ImmutableSolidColorBrush(4293454056u);
		button29.Foreground = new ImmutableSolidColorBrush(4284900966u);
		button29.BorderThickness = new Thickness(0.0, 0.0, 0.0, 0.0);
		button29.CornerRadius = new CornerRadius(6.0, 6.0, 6.0, 6.0);
		context.PopParent();
		((ISupportInitialize)button28).EndInit();
		context.PopParent();
		((ISupportInitialize)stackPanel56).EndInit();
		context.PopParent();
		((ISupportInitialize)stackPanel52).EndInit();
		context.PopParent();
		((ISupportInitialize)grid20).EndInit();
		Controls children46 = stackPanel5.Children;
		Border border15;
		Border border14 = (border15 = new Border());
		((ISupportInitialize)border14).BeginInit();
		children46.Add(border14);
		border15.Background = new ImmutableSolidColorBrush(16777215u);
		border15.CornerRadius = new CornerRadius(8.0, 8.0, 8.0, 8.0);
		border15.Margin = new Thickness(30.0, 0.0, 30.0, 0.0);
		border15.Padding = new Thickness(15.0, 12.0, 15.0, 12.0);
		StackPanel stackPanel59;
		StackPanel stackPanel58 = (stackPanel59 = new StackPanel());
		((ISupportInitialize)stackPanel58).BeginInit();
		border15.Child = stackPanel58;
		stackPanel59.Orientation = Orientation.Vertical;
		stackPanel59.HorizontalAlignment = HorizontalAlignment.Center;
		stackPanel59.VerticalAlignment = VerticalAlignment.Center;
		Controls children47 = stackPanel59.Children;
		TextBlock textBlock61;
		TextBlock textBlock60 = (textBlock61 = new TextBlock());
		((ISupportInitialize)textBlock60).BeginInit();
		children47.Add(textBlock60);
		textBlock61.FontSize = 17.0;
		textBlock61.Foreground = new ImmutableSolidColorBrush(4279592384u);
		textBlock61.FontWeight = FontWeight.Bold;
		textBlock61.VerticalAlignment = VerticalAlignment.Center;
		textBlock61.HorizontalAlignment = HorizontalAlignment.Center;
		textBlock61.TextAlignment = TextAlignment.Center;
		InlineCollection? inlines = textBlock61.Inlines;
		Run run = new Run();
		((ISupportInitialize)run).BeginInit();
		run.Text = "✨ 支持Claude3.7等模型无限次数对话 ✨";
		run.FontWeight = FontWeight.Bold;
		((ISupportInitialize)run).EndInit();
		inlines.Add(run);
		InlineCollection? inlines2 = textBlock61.Inlines;
		LineBreak lineBreak = new LineBreak();
		((ISupportInitialize)lineBreak).BeginInit();
		((ISupportInitialize)lineBreak).EndInit();
		inlines2.Add(lineBreak);
		InlineCollection? inlines3 = textBlock61.Inlines;
		LineBreak lineBreak2 = new LineBreak();
		((ISupportInitialize)lineBreak2).BeginInit();
		((ISupportInitialize)lineBreak2).EndInit();
		inlines3.Add(lineBreak2);
		((ISupportInitialize)textBlock61).EndInit();
		((ISupportInitialize)stackPanel59).EndInit();
		((ISupportInitialize)border15).EndInit();
		Controls children48 = stackPanel5.Children;
		Button button31;
		Button button30 = (button31 = new Button());
		((ISupportInitialize)button30).BeginInit();
		children48.Add(button30);
		Button button32 = (button4 = button31);
		context.PushParent(button4);
		Button button33 = button4;
		button33.HorizontalAlignment = HorizontalAlignment.Center;
		button33.Margin = new Thickness(0.0, -15.0, 0.0, 0.0);
		button33.Background = new ImmutableSolidColorBrush(16777215u);
		button33.BorderThickness = new Thickness(0.0, 0.0, 0.0, 0.0);
		button33.Padding = new Thickness(8.0, 4.0, 8.0, 4.0);
		StyledProperty<ICommand?> commandProperty7 = Button.CommandProperty;
		CompiledBindingExtension compiledBindingExtension21 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E恢复购买记录Command_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = Button.CommandProperty;
		CompiledBindingExtension binding21 = compiledBindingExtension21.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(button33, commandProperty7, (IBinding)binding21);
		button33.Cursor = new Cursor(StandardCursorType.Hand);
		StyledProperty<bool> isVisibleProperty9 = Visual.IsVisibleProperty;
		CompiledBindingExtension compiledBindingExtension22 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E显示恢复购买按钮_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = Visual.IsVisibleProperty;
		CompiledBindingExtension binding22 = compiledBindingExtension22.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(button33, isVisibleProperty9, (IBinding)binding22);
		TextBlock textBlock63;
		TextBlock textBlock62 = (textBlock63 = new TextBlock());
		((ISupportInitialize)textBlock62).BeginInit();
		button33.Content = textBlock62;
		textBlock63.Text = "恢复购买记录";
		textBlock63.FontSize = 12.0;
		textBlock63.Foreground = new ImmutableSolidColorBrush(4288455599u);
		textBlock63.HorizontalAlignment = HorizontalAlignment.Center;
		((ISupportInitialize)textBlock63).EndInit();
		context.PopParent();
		((ISupportInitialize)button32).EndInit();
		context.PopParent();
		((ISupportInitialize)stackPanel3).EndInit();
		context.PopParent();
		((ISupportInitialize)grid3).EndInit();
		context.PopParent();
		((ISupportInitialize)border3).EndInit();
		context.PopParent();
		((ISupportInitialize)P_1).EndInit();
		if (P_1 is StyledElement styled)
		{
			NameScope.SetNameScope(styled, context.AvaloniaNameScope);
		}
		context.AvaloniaNameScope.Complete();
	}

	private static void _0021XamlIlPopulateTrampoline(购买卡密 P_0)
	{
		if (_0021XamlIlPopulateOverride != null)
		{
			_0021XamlIlPopulateOverride(P_0);
		}
		else
		{
			_0021XamlIlPopulate(XamlIlRuntimeHelpers.CreateRootServiceProviderV3(null), P_0);
		}
	}
}
