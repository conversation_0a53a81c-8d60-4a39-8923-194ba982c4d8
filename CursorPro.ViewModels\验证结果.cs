using System.Text.Json;
using System.Text.Json.Serialization;

namespace CursorPro.ViewModels;

public class 验证结果
{
	[JsonPropertyName("success")]
	public bool Success { get; set; }

	[JsonPropertyName("message")]
	public string Message { get; set; } = string.Empty;

	[JsonPropertyName("trial_count")]
	public int? TrialCount { get; set; }

	[JsonPropertyName("need_update")]
	public bool? NeedUpdate { get; set; }

	[JsonPropertyName("latest_version")]
	public string? LatestVersion { get; set; }

	[JsonPropertyName("account_info")]
	public 账号信息? AccountInfo { get; set; }

	[JsonPropertyName("key_info")]
	public JsonElement? KeyInfo { get; set; }

	[JsonPropertyName("expiry_time")]
	public string? ExpiryTime { get; set; }

	[JsonPropertyName("is_fake")]
	public bool? IsFake { get; set; }

	[Json<PERSON>ropertyName("proxyServer")]
	public string? ProxyServer { get; set; }
}
