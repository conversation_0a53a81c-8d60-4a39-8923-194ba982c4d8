using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace CursorPro.Models;

public static class HttpService
{
	private static readonly HttpClient _httpClient;

	private const int DefaultTimeoutSeconds = 10;

	private static readonly JsonSerializerOptions _jsonOptions;

	static HttpService()
	{
		_jsonOptions = new JsonSerializerOptions
		{
			PropertyNameCaseInsensitive = true,
			PropertyNamingPolicy = JsonNamingPolicy.CamelCase
		};
		_httpClient = new HttpClient
		{
			Timeout = TimeSpan.FromSeconds(10.0)
		};
		_httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
	}

	public static async Task<TResponse?> PostAsync<TRequest, TResponse>(string url, TRequest data, int timeoutSeconds = 10)
	{
		using CancellationTokenSource cts = new CancellationTokenSource(TimeSpan.FromSeconds(timeoutSeconds));
		_ = 1;
		try
		{
			using HttpResponseMessage response = await _httpClient.PostAsJsonAsync(url, data, cts.Token);
			response.EnsureSuccessStatusCode();
			return JsonSerializer.Deserialize<TResponse>(await response.Content.ReadAsStringAsync(cts.Token), _jsonOptions);
		}
		catch (OperationCanceledException)
		{
			throw new TimeoutException("请求超时: " + url);
		}
		catch (HttpRequestException)
		{
			throw;
		}
		catch (Exception)
		{
			throw;
		}
	}

	public static async Task<TResponse?> GetAsync<TResponse>(string url, int timeoutSeconds = 10)
	{
		using CancellationTokenSource cts = new CancellationTokenSource(TimeSpan.FromSeconds(timeoutSeconds));
		_ = 1;
		try
		{
			using HttpResponseMessage response = await _httpClient.GetAsync(url, cts.Token);
			response.EnsureSuccessStatusCode();
			return JsonSerializer.Deserialize<TResponse>(await response.Content.ReadAsStringAsync(cts.Token), _jsonOptions);
		}
		catch (OperationCanceledException)
		{
			throw new TimeoutException("请求超时: " + url);
		}
		catch (HttpRequestException)
		{
			throw;
		}
		catch (Exception)
		{
			throw;
		}
	}
}
