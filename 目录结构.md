# CursorPro 目录结构文档

## 项目根目录

```
CursorPro/
├── .gitignore                    # Git忽略文件配置
├── .gitattributes               # Git属性配置
├── src_Temp6.sln               # Visual Studio解决方案文件
├── CursorPro.csproj            # 项目配置文件
├── app.ico                     # 应用程序图标
├── app.manifest                # Windows应用程序清单
└── Properties/                 # 程序集属性目录
    └── AssemblyInfo.cs         # 程序集信息配置
```

## 核心代码目录

### CursorPro/ (主程序目录)
```
CursorPro/
├── Program.cs                  # 程序入口点，配置Avalonia应用
├── App.axaml                   # 应用程序XAML定义
├── App.axaml.cs               # 应用程序代码后置
├── Info.cs                     # 全局配置信息类
└── ViewLocator.cs             # 视图定位器，MVVM模式支持
```

### CursorPro.Models/ (数据模型目录)
```
CursorPro.Models/
├── CursorProcessManager.cs     # Cursor进程管理器
│   ├── GetCursorPath()        # 获取Cursor安装路径
│   ├── StartCursorAsync()     # 异步启动Cursor
│   ├── CloseCursorAsync()     # 异步关闭Cursor
│   └── 跨平台进程管理逻辑
├── HttpService.cs             # HTTP网络服务类
│   ├── PostAsync<T,R>()       # 异步POST请求
│   ├── GetAsync<T>()          # 异步GET请求
│   └── 网络异常处理
├── LocalConfig.cs             # 本地配置管理
│   ├── 读取(key)              # 读取配置值
│   ├── 写入(key, value)       # 写入配置值
│   └── 配置文件加密存储
├── Tools.cs                   # 工具类集合
│   ├── OpenUrl()              # 跨平台打开URL
│   ├── 显示消息框Async()       # 异步消息框
│   └── 显示确认消息框Async()   # 异步确认对话框
└── 验证结果.cs                # API验证结果模型
    ├── Success                # 验证是否成功
    ├── Message               # 返回消息
    ├── NeedUpdate            # 是否需要更新
    └── ExpiryTime            # 到期时间
```

### CursorPro.ViewModels/ (视图模型目录)
```
CursorPro.ViewModels/
├── ViewModelBase.cs           # MVVM基础视图模型
├── 主要窗口.cs                # 主窗口视图模型
│   ├── 一键获取快速额度Command # 重置机器码命令
│   ├── 加速CursorCommand      # 修改Cursor配置命令
│   ├── 购买卡密Command        # 跳转购买界面命令
│   └── 界面状态管理属性
├── 检查限制.cs                # 账号验证逻辑
│   ├── 验证试用账号()         # 试用账号验证
│   ├── 验证付费账号()         # 付费账号验证
│   ├── 启动检查()             # 程序启动时验证
│   └── 处理版本更新()         # 版本更新处理
├── 获取设备标识.cs            # 设备标识生成器
│   ├── 获取机器码()           # 生成唯一机器码
│   ├── 获取物理MAC地址()      # 获取网卡MAC地址
│   ├── 生成随机机器码()       # 生成随机标识
│   └── 设备信息缓存机制
├── 重置机器码.cs              # 机器码重置功能
│   ├── 执行重置机器码()       # 主要重置逻辑
│   ├── 清除Cursor配置()       # 清除配置文件
│   ├── 重新生成标识()         # 重新生成设备标识
│   └── 跨平台文件操作
├── 界面修改器.cs              # Cursor界面修改器
│   ├── 修改Workbench文件()    # 修改主界面文件
│   ├── 获取Cursor主文件路径() # 获取关键文件路径
│   ├── 隐藏付费提示()         # 移除付费相关UI
│   └── 界面元素定制化
├── 购买卡密VM.cs              # 购买卡密视图模型
│   ├── 选择卡密Command        # 选择套餐命令
│   ├── 创建支付链接()         # 生成支付订单
│   ├── 查询订单状态()         # 检查支付状态
│   ├── 恢复购买记录Command    # 恢复购买记录
│   └── 支付流程管理
└── PlatformPaths.cs           # 平台路径管理器
    ├── InitializeWindowsPaths() # Windows路径初始化
    ├── InitializeMacPaths()     # Mac路径初始化
    ├── DbPath                   # 数据库文件路径
    ├── SqlitePath              # SQLite数据库路径
    ├── WorkbenchPath           # Workbench文件路径
    └── 跨平台路径适配
```

### CursorPro.Views/ (视图目录)
```
CursorPro.Views/
├── 主要窗口.axaml             # 主窗口XAML布局
├── 主要窗口.axaml.cs          # 主窗口代码后置
├── 购买卡密.axaml             # 购买界面XAML布局
├── 购买卡密.axaml.cs          # 购买界面代码后置
└── UserControls/              # 用户控件目录
    └── 自定义控件集合
```

### CompiledAvaloniaXaml/ (编译生成的XAML目录)
```
CompiledAvaloniaXaml/
├── !AvaloniaResources.cs      # Avalonia资源编译文件
├── !XamlLoader.cs             # XAML加载器
├── _0021XamlLoader.cs         # 自动生成的XAML加载器
└── 各种编译生成的XAML相关文件
```

## 配置文件详解

### CursorPro.csproj
```xml
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>netcoreapp8.0</TargetFramework>  <!-- 目标框架 -->
    <PlatformTarget>x64</PlatformTarget>               <!-- 平台目标 -->
    <ApplicationIcon>app.ico</ApplicationIcon>         <!-- 应用图标 -->
    <ApplicationManifest>app.manifest</ApplicationManifest> <!-- Windows清单 -->
  </PropertyGroup>
</Project>
```

### app.manifest
- Windows应用程序清单文件
- 定义应用程序权限和兼容性
- 支持高DPI和现代Windows特性

### .gitignore
- 标准的Visual Studio .gitignore配置
- 忽略编译输出、临时文件、用户配置等
- 包含.NET Core特定的忽略规则

## 文件作用总结

| 文件类型 | 主要作用 | 关键文件 |
|---------|---------|---------|
| 程序入口 | 应用启动和配置 | Program.cs, App.axaml |
| 数据模型 | 业务逻辑和数据处理 | CursorProcessManager.cs, HttpService.cs |
| 视图模型 | MVVM模式的业务逻辑 | 主要窗口.cs, 检查限制.cs |
| 视图界面 | 用户界面定义 | *.axaml文件 |
| 配置管理 | 项目和应用配置 | Info.cs, LocalConfig.cs |
| 工具类 | 通用功能封装 | Tools.cs, PlatformPaths.cs |
| 编译输出 | 自动生成的代码 | CompiledAvaloniaXaml/ |

## 反编译特征

1. **参数名混淆**：方法参数名变成P_0, P_1等
2. **自动生成类**：带有数字前缀的类名如_0021XamlLoader
3. **中文命名**：大量使用中文类名和方法名
4. **编译器标记**：[GeneratedCode]等属性标记
5. **XAML编译**：CompiledAvaloniaXaml目录包含编译后的XAML代码