using System;
using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using Avalonia;
using Avalonia.Animation;
using Avalonia.Controls;
using Avalonia.Controls.Documents;
using Avalonia.Controls.Presenters;
using Avalonia.Controls.Primitives;
using Avalonia.Controls.Shapes;
using Avalonia.Data;
using Avalonia.Input;
using Avalonia.Layout;
using Avalonia.Markup.Xaml.Converters;
using Avalonia.Markup.Xaml.MarkupExtensions;
using Avalonia.Markup.Xaml.MarkupExtensions.CompiledBindings;
using Avalonia.Markup.Xaml.XamlIl.Runtime;
using Avalonia.Media;
using Avalonia.Media.Immutable;
using Avalonia.Styling;
using CompiledAvaloniaXaml;

namespace CursorPro.Views;

[CompilerGenerated]
public class 主要窗口 : Window
{
	[GeneratedCode("Avalonia.Generators.NameGenerator.InitializeComponentCodeGenerator", "11.2.1.0")]
	internal Button SwitchAccountButton;

	[GeneratedCode("Avalonia.Generators.NameGenerator.InitializeComponentCodeGenerator", "11.2.1.0")]
	internal Button TutorialButton;

	[GeneratedCode("Avalonia.Generators.NameGenerator.InitializeComponentCodeGenerator", "11.2.1.0")]
	internal CheckBox AccelerateCursorCheckBox;

	private static Action<object> _0021XamlIlPopulateOverride;

	public 主要窗口()
	{
		InitializeComponent();
		base.Closed += 主要窗口_Closed;
	}

	private void 主要窗口_Closed(object? sender, EventArgs e)
	{
		try
		{
			if (base.DataContext is IDisposable disposable)
			{
				disposable.Dispose();
			}
			base.DataContext = null;
		}
		catch (Exception)
		{
		}
	}

	[GeneratedCode("Avalonia.Generators.NameGenerator.InitializeComponentCodeGenerator", "11.2.1.0")]
	[ExcludeFromCodeCoverage]
	public void InitializeComponent(bool loadXaml = true)
	{
		if (loadXaml)
		{
			_0021XamlIlPopulateTrampoline(this);
		}
		INameScope nameScope = this.FindNameScope();
		SwitchAccountButton = nameScope?.Find<Button>("SwitchAccountButton");
		TutorialButton = nameScope?.Find<Button>("TutorialButton");
		AccelerateCursorCheckBox = nameScope?.Find<CheckBox>("AccelerateCursorCheckBox");
	}

	private static void _0021XamlIlPopulate(IServiceProvider P_0, 主要窗口 P_1)
	{
		CompiledAvaloniaXaml.XamlIlContext.Context<主要窗口> context = new CompiledAvaloniaXaml.XamlIlContext.Context<主要窗口>(P_0, new object[1] { _0021AvaloniaResources.NamespaceInfo_003A_002FViews_002F主要窗口_002Eaxaml.Singleton }, "avares://CursorPro/Views/主要窗口.axaml")
		{
			RootObject = P_1,
			IntermediateRoot = P_1
		};
		((ISupportInitialize)P_1).BeginInit();
		context.PushParent(P_1);
		P_1.Width = 340.0;
		P_1.Height = 400.0;
		P_1.WindowStartupLocation = WindowStartupLocation.CenterScreen;
		P_1.CanResize = false;
		// 注释掉有问题的编译绑定代码
		// StyledProperty<string?> titleProperty = Window.TitleProperty;
		// CompiledBindingExtension compiledBindingExtension = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E窗口标题_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		// context.ProvideTargetProperty = Window.TitleProperty;
		// CompiledBindingExtension binding = compiledBindingExtension.ProvideValue(context);
		// context.ProvideTargetProperty = null;
		// AvaloniaObjectExtensions.Bind(P_1, titleProperty, (IBinding)binding);
		P_1.Background = new SolidColorBrush
		{
			Color = Color.FromUInt32(uint.MaxValue)
		};
		DropShadowEffect dropShadowEffect = new DropShadowEffect();
		dropShadowEffect.BlurRadius = 20.0;
		dropShadowEffect.Opacity = 0.4;
		dropShadowEffect.OffsetX = 0.0;
		dropShadowEffect.OffsetY = 4.0;
		dropShadowEffect.Color = Color.FromUInt32(4278190080u);
		P_1.Effect = dropShadowEffect;
		Styles styles = P_1.Styles;
		Style style = new Style();
		style.Selector = ((Selector?)null).OfType(typeof(Button));
		Setter setter = new Setter();
		setter.Property = InputElement.CursorProperty;
		setter.Value = new Cursor(StandardCursorType.Hand);
		style.Add(setter);
		Setter setter2 = new Setter();
		setter2.Property = TemplatedControl.BorderThicknessProperty;
		setter2.Value = new Thickness(1.0, 1.0, 1.0, 1.0);
		style.Add(setter2);
		Setter setter3 = new Setter();
		setter3.Property = Animatable.TransitionsProperty;
		Transitions transitions = new Transitions();
		TransformOperationsTransition transformOperationsTransition = new TransformOperationsTransition();
		transformOperationsTransition.Property = Visual.RenderTransformProperty;
		transformOperationsTransition.Duration = TimeSpan.FromTicks(1500000L);
		transitions.Add(transformOperationsTransition);
		BrushTransition brushTransition = new BrushTransition();
		brushTransition.Property = TemplatedControl.BackgroundProperty;
		brushTransition.Duration = TimeSpan.FromTicks(1500000L);
		transitions.Add(brushTransition);
		BrushTransition brushTransition2 = new BrushTransition();
		brushTransition2.Property = TemplatedControl.BorderBrushProperty;
		brushTransition2.Duration = TimeSpan.FromTicks(1500000L);
		transitions.Add(brushTransition2);
		setter3.Value = transitions;
		style.Add(setter3);
		styles.Add(style);
		Styles styles2 = P_1.Styles;
		Style style2;
		Style item = (style2 = new Style());
		context.PushParent(style2);
		Style style3 = style2;
		style3.Selector = ((Selector?)null).OfType(typeof(Button)).Class(":pointerover").Template()
			.OfType(typeof(ContentPresenter));
		Setter setter5;
		Setter setter4 = (setter5 = new Setter());
		context.PushParent(setter5);
		Setter setter6 = setter5;
		setter6.Property = ContentPresenter.BackgroundProperty;
		// CompiledBindingExtension compiledBindingExtension2 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Ancestor(typeof(Button), 0).Property(TemplatedControl.BackgroundProperty, PropertyInfoAccessorFactory.CreateAvaloniaPropertyAccessor).Build());
		// context.ProvideTargetProperty = CompiledAvaloniaXaml.XamlIlHelpers.Avalonia_002EStyling_002ESetter_002CAvalonia_002EBase_002EValue_0021Property();
		// CompiledBindingExtension value = compiledBindingExtension2.ProvideValue(context);
		// context.ProvideTargetProperty = null;
		// setter6.Value = value;
		context.PopParent();
		style3.Add(setter4);
		Setter setter7 = new Setter();
		setter7.Property = TextElement.ForegroundProperty;
		setter7.Value = new ImmutableSolidColorBrush(uint.MaxValue);
		style3.Add(setter7);
		context.PopParent();
		styles2.Add(item);
		Styles styles3 = P_1.Styles;
		Style style4 = new Style();
		style4.Selector = ((Selector?)null).OfType(typeof(Button)).Class("primary");
		Setter setter8 = new Setter();
		setter8.Property = TemplatedControl.BackgroundProperty;
		setter8.Value = new ImmutableSolidColorBrush(4282339765u);
		style4.Add(setter8);
		Setter setter9 = new Setter();
		setter9.Property = TemplatedControl.ForegroundProperty;
		setter9.Value = new ImmutableSolidColorBrush(uint.MaxValue);
		style4.Add(setter9);
		Setter setter10 = new Setter();
		setter10.Property = TemplatedControl.BorderBrushProperty;
		setter10.Value = new ImmutableSolidColorBrush(4282339765u);
		style4.Add(setter10);
		styles3.Add(style4);
		Styles styles4 = P_1.Styles;
		Style style5 = new Style();
		style5.Selector = ((Selector?)null).OfType(typeof(Button)).Class("primary").Class(":pointerover")
			.Template()
			.OfType(typeof(ContentPresenter));
		Setter setter11 = new Setter();
		setter11.Property = ContentPresenter.BackgroundProperty;
		setter11.Value = new ImmutableSolidColorBrush(4283392453u);
		style5.Add(setter11);
		Setter setter12 = new Setter();
		setter12.Property = TextElement.ForegroundProperty;
		setter12.Value = new ImmutableSolidColorBrush(uint.MaxValue);
		style5.Add(setter12);
		styles4.Add(style5);
		Styles styles5 = P_1.Styles;
		Style style6 = new Style();
		style6.Selector = ((Selector?)null).OfType(typeof(Button)).Class("primary").Class(":pressed")
			.Template()
			.OfType(typeof(ContentPresenter));
		Setter setter13 = new Setter();
		setter13.Property = ContentPresenter.BackgroundProperty;
		setter13.Value = new ImmutableSolidColorBrush(4281681317u);
		style6.Add(setter13);
		Setter setter14 = new Setter();
		setter14.Property = TextElement.ForegroundProperty;
		setter14.Value = new ImmutableSolidColorBrush(uint.MaxValue);
		style6.Add(setter14);
		styles5.Add(style6);
		Styles styles6 = P_1.Styles;
		Style item2 = (style2 = new Style());
		context.PushParent(style2);
		Style style7 = style2;
		style7.Selector = ((Selector?)null).OfType(typeof(Button)).Class("primary").Class(":pointerover");
		Setter setter15 = new Setter();
		setter15.Property = TemplatedControl.ForegroundProperty;
		setter15.Value = new ImmutableSolidColorBrush(uint.MaxValue);
		style7.Add(setter15);
		Setter setter16 = (setter5 = new Setter());
		context.PushParent(setter5);
		Setter setter17 = setter5;
		setter17.Property = Visual.RenderTransformProperty;
		setter17.Value = (ITransform)new TransformConverter().ConvertFrom(context, CultureInfo.InvariantCulture, "scale(1.02)");
		context.PopParent();
		style7.Add(setter16);
		context.PopParent();
		styles6.Add(item2);
		Styles styles7 = P_1.Styles;
		Style item3 = (style2 = new Style());
		context.PushParent(style2);
		Style style8 = style2;
		style8.Selector = ((Selector?)null).OfType(typeof(Button)).Class("primary").Class(":pressed");
		Setter setter18 = new Setter();
		setter18.Property = TemplatedControl.ForegroundProperty;
		setter18.Value = new ImmutableSolidColorBrush(uint.MaxValue);
		style8.Add(setter18);
		Setter setter19 = (setter5 = new Setter());
		context.PushParent(setter5);
		Setter setter20 = setter5;
		setter20.Property = Visual.RenderTransformProperty;
		setter20.Value = (ITransform)new TransformConverter().ConvertFrom(context, CultureInfo.InvariantCulture, "scale(0.98)");
		context.PopParent();
		style8.Add(setter19);
		context.PopParent();
		styles7.Add(item3);
		Styles styles8 = P_1.Styles;
		Style style9 = new Style();
		style9.Selector = ((Selector?)null).OfType(typeof(Button)).Class("secondary");
		Setter setter21 = new Setter();
		setter21.Property = TemplatedControl.BackgroundProperty;
		setter21.Value = new ImmutableSolidColorBrush(4294309365u);
		style9.Add(setter21);
		Setter setter22 = new Setter();
		setter22.Property = TemplatedControl.ForegroundProperty;
		setter22.Value = new ImmutableSolidColorBrush(4282339765u);
		style9.Add(setter22);
		Setter setter23 = new Setter();
		setter23.Property = TemplatedControl.BorderBrushProperty;
		setter23.Value = new ImmutableSolidColorBrush(4282339765u);
		style9.Add(setter23);
		styles8.Add(style9);
		Styles styles9 = P_1.Styles;
		Style style10 = new Style();
		style10.Selector = ((Selector?)null).OfType(typeof(Button)).Class("secondary").Class(":pointerover")
			.Template()
			.OfType(typeof(ContentPresenter));
		Setter setter24 = new Setter();
		setter24.Property = ContentPresenter.BackgroundProperty;
		setter24.Value = new ImmutableSolidColorBrush(4293848814u);
		style10.Add(setter24);
		Setter setter25 = new Setter();
		setter25.Property = ContentPresenter.BorderBrushProperty;
		setter25.Value = new ImmutableSolidColorBrush(4283392453u);
		style10.Add(setter25);
		Setter setter26 = new Setter();
		setter26.Property = TextElement.ForegroundProperty;
		setter26.Value = new ImmutableSolidColorBrush(4282339765u);
		style10.Add(setter26);
		styles9.Add(style10);
		Styles styles10 = P_1.Styles;
		Style style11 = new Style();
		style11.Selector = ((Selector?)null).OfType(typeof(Button)).Class("secondary").Class(":pressed")
			.Template()
			.OfType(typeof(ContentPresenter));
		Setter setter27 = new Setter();
		setter27.Property = ContentPresenter.BackgroundProperty;
		setter27.Value = new ImmutableSolidColorBrush(4292927712u);
		style11.Add(setter27);
		Setter setter28 = new Setter();
		setter28.Property = ContentPresenter.BorderBrushProperty;
		setter28.Value = new ImmutableSolidColorBrush(4281681317u);
		style11.Add(setter28);
		Setter setter29 = new Setter();
		setter29.Property = TextElement.ForegroundProperty;
		setter29.Value = new ImmutableSolidColorBrush(4282339765u);
		style11.Add(setter29);
		styles10.Add(style11);
		Styles styles11 = P_1.Styles;
		Style item4 = (style2 = new Style());
		context.PushParent(style2);
		Style style12 = style2;
		style12.Selector = ((Selector?)null).OfType(typeof(Button)).Class("secondary").Class(":pointerover");
		Setter setter30 = new Setter();
		setter30.Property = TemplatedControl.ForegroundProperty;
		setter30.Value = new ImmutableSolidColorBrush(4282339765u);
		style12.Add(setter30);
		Setter setter31 = (setter5 = new Setter());
		context.PushParent(setter5);
		Setter setter32 = setter5;
		setter32.Property = Visual.RenderTransformProperty;
		setter32.Value = (ITransform)new TransformConverter().ConvertFrom(context, CultureInfo.InvariantCulture, "scale(1.02)");
		context.PopParent();
		style12.Add(setter31);
		context.PopParent();
		styles11.Add(item4);
		Styles styles12 = P_1.Styles;
		Style item5 = (style2 = new Style());
		context.PushParent(style2);
		Style style13 = style2;
		style13.Selector = ((Selector?)null).OfType(typeof(Button)).Class("secondary").Class(":pressed");
		Setter setter33 = new Setter();
		setter33.Property = TemplatedControl.ForegroundProperty;
		setter33.Value = new ImmutableSolidColorBrush(4282339765u);
		style13.Add(setter33);
		Setter setter34 = (setter5 = new Setter());
		context.PushParent(setter5);
		Setter setter35 = setter5;
		setter35.Property = Visual.RenderTransformProperty;
		setter35.Value = (ITransform)new TransformConverter().ConvertFrom(context, CultureInfo.InvariantCulture, "scale(0.98)");
		context.PopParent();
		style13.Add(setter34);
		context.PopParent();
		styles12.Add(item5);
		Styles styles13 = P_1.Styles;
		Style style14 = new Style();
		style14.Selector = ((Selector?)null).OfType(typeof(Button)).Class("accent");
		Setter setter36 = new Setter();
		setter36.Property = TemplatedControl.BackgroundProperty;
		setter36.Value = new ImmutableSolidColorBrush(4293212469u);
		style14.Add(setter36);
		Setter setter37 = new Setter();
		setter37.Property = TemplatedControl.ForegroundProperty;
		setter37.Value = new ImmutableSolidColorBrush(uint.MaxValue);
		style14.Add(setter37);
		Setter setter38 = new Setter();
		setter38.Property = TemplatedControl.BorderBrushProperty;
		setter38.Value = new ImmutableSolidColorBrush(4293212469u);
		style14.Add(setter38);
		styles13.Add(style14);
		Styles styles14 = P_1.Styles;
		Style style15 = new Style();
		style15.Selector = ((Selector?)null).OfType(typeof(Button)).Class("accent").Class(":pointerover")
			.Template()
			.OfType(typeof(ContentPresenter));
		Setter setter39 = new Setter();
		setter39.Property = ContentPresenter.BackgroundProperty;
		setter39.Value = new ImmutableSolidColorBrush(4293874512u);
		style15.Add(setter39);
		styles14.Add(style15);
		Styles styles15 = P_1.Styles;
		Style style16 = new Style();
		style16.Selector = ((Selector?)null).OfType(typeof(Button)).Class("accent").Class(":pressed")
			.Template()
			.OfType(typeof(ContentPresenter));
		Setter setter40 = new Setter();
		setter40.Property = ContentPresenter.BackgroundProperty;
		setter40.Value = new ImmutableSolidColorBrush(4292030255u);
		style16.Add(setter40);
		styles15.Add(style16);
		Styles styles16 = P_1.Styles;
		Style item6 = (style2 = new Style());
		context.PushParent(style2);
		Style style17 = style2;
		style17.Selector = ((Selector?)null).OfType(typeof(Button)).Class("accent").Class(":pointerover");
		Setter setter41 = (setter5 = new Setter());
		context.PushParent(setter5);
		Setter setter42 = setter5;
		setter42.Property = Visual.RenderTransformProperty;
		setter42.Value = (ITransform)new TransformConverter().ConvertFrom(context, CultureInfo.InvariantCulture, "scale(1.02)");
		context.PopParent();
		style17.Add(setter41);
		context.PopParent();
		styles16.Add(item6);
		Styles styles17 = P_1.Styles;
		Style item7 = (style2 = new Style());
		context.PushParent(style2);
		Style style18 = style2;
		style18.Selector = ((Selector?)null).OfType(typeof(Button)).Class("accent").Class(":pressed");
		Setter setter43 = (setter5 = new Setter());
		context.PushParent(setter5);
		Setter setter44 = setter5;
		setter44.Property = Visual.RenderTransformProperty;
		setter44.Value = (ITransform)new TransformConverter().ConvertFrom(context, CultureInfo.InvariantCulture, "scale(0.98)");
		context.PopParent();
		style18.Add(setter43);
		context.PopParent();
		styles17.Add(item7);
		Styles styles18 = P_1.Styles;
		Style style19 = new Style();
		style19.Selector = ((Selector?)null).OfType(typeof(Button)).Class("success");
		Setter setter45 = new Setter();
		setter45.Property = TemplatedControl.BackgroundProperty;
		setter45.Value = new ImmutableSolidColorBrush(4284922730u);
		style19.Add(setter45);
		Setter setter46 = new Setter();
		setter46.Property = TemplatedControl.ForegroundProperty;
		setter46.Value = new ImmutableSolidColorBrush(uint.MaxValue);
		style19.Add(setter46);
		Setter setter47 = new Setter();
		setter47.Property = TemplatedControl.BorderBrushProperty;
		setter47.Value = new ImmutableSolidColorBrush(4284922730u);
		style19.Add(setter47);
		styles18.Add(style19);
		Styles styles19 = P_1.Styles;
		Style style20 = new Style();
		style20.Selector = ((Selector?)null).OfType(typeof(Button)).Class("success").Class(":pointerover")
			.Template()
			.OfType(typeof(ContentPresenter));
		Setter setter48 = new Setter();
		setter48.Property = ContentPresenter.BackgroundProperty;
		setter48.Value = new ImmutableSolidColorBrush(4286695300u);
		style20.Add(setter48);
		Setter setter49 = new Setter();
		setter49.Property = TextElement.ForegroundProperty;
		setter49.Value = new ImmutableSolidColorBrush(uint.MaxValue);
		style20.Add(setter49);
		styles19.Add(style20);
		Styles styles20 = P_1.Styles;
		Style style21 = new Style();
		style21.Selector = ((Selector?)null).OfType(typeof(Button)).Class("success").Class(":pressed")
			.Template()
			.OfType(typeof(ContentPresenter));
		Setter setter50 = new Setter();
		setter50.Property = ContentPresenter.BackgroundProperty;
		setter50.Value = new ImmutableSolidColorBrush(4283215696u);
		style21.Add(setter50);
		Setter setter51 = new Setter();
		setter51.Property = TextElement.ForegroundProperty;
		setter51.Value = new ImmutableSolidColorBrush(uint.MaxValue);
		style21.Add(setter51);
		styles20.Add(style21);
		Styles styles21 = P_1.Styles;
		Style item8 = (style2 = new Style());
		context.PushParent(style2);
		Style style22 = style2;
		style22.Selector = ((Selector?)null).OfType(typeof(Button)).Class("success").Class(":pointerover");
		Setter setter52 = new Setter();
		setter52.Property = TemplatedControl.ForegroundProperty;
		setter52.Value = new ImmutableSolidColorBrush(uint.MaxValue);
		style22.Add(setter52);
		Setter setter53 = (setter5 = new Setter());
		context.PushParent(setter5);
		Setter setter54 = setter5;
		setter54.Property = Visual.RenderTransformProperty;
		setter54.Value = (ITransform)new TransformConverter().ConvertFrom(context, CultureInfo.InvariantCulture, "scale(1.02)");
		context.PopParent();
		style22.Add(setter53);
		Setter setter55 = new Setter();
		setter55.Property = Visual.EffectProperty;
		DropShadowEffect dropShadowEffect2 = new DropShadowEffect();
		dropShadowEffect2.BlurRadius = 8.0;
		dropShadowEffect2.Opacity = 0.3;
		dropShadowEffect2.OffsetX = 0.0;
		dropShadowEffect2.OffsetY = 2.0;
		setter55.Value = dropShadowEffect2;
		style22.Add(setter55);
		context.PopParent();
		styles21.Add(item8);
		Styles styles22 = P_1.Styles;
		Style item9 = (style2 = new Style());
		context.PushParent(style2);
		Style style23 = style2;
		style23.Selector = ((Selector?)null).OfType(typeof(Button)).Class("success").Class(":pressed");
		Setter setter56 = new Setter();
		setter56.Property = TemplatedControl.ForegroundProperty;
		setter56.Value = new ImmutableSolidColorBrush(uint.MaxValue);
		style23.Add(setter56);
		Setter setter57 = (setter5 = new Setter());
		context.PushParent(setter5);
		Setter setter58 = setter5;
		setter58.Property = Visual.RenderTransformProperty;
		setter58.Value = (ITransform)new TransformConverter().ConvertFrom(context, CultureInfo.InvariantCulture, "scale(0.98)");
		context.PopParent();
		style23.Add(setter57);
		context.PopParent();
		styles22.Add(item9);
		Styles styles23 = P_1.Styles;
		Style style24 = new Style();
		style24.Selector = ((Selector?)null).OfType(typeof(Button)).Class("link");
		Setter setter59 = new Setter();
		setter59.Property = InputElement.CursorProperty;
		setter59.Value = new Cursor(StandardCursorType.Hand);
		style24.Add(setter59);
		Setter setter60 = new Setter();
		setter60.Property = TemplatedControl.BackgroundProperty;
		setter60.Value = new ImmutableSolidColorBrush(16777215u);
		style24.Add(setter60);
		Setter setter61 = new Setter();
		setter61.Property = TemplatedControl.BorderThicknessProperty;
		setter61.Value = new Thickness(0.0, 0.0, 0.0, 0.0);
		style24.Add(setter61);
		Setter setter62 = new Setter();
		setter62.Property = TemplatedControl.PaddingProperty;
		setter62.Value = new Thickness(5.0, 2.0, 5.0, 2.0);
		style24.Add(setter62);
		Setter setter63 = new Setter();
		setter63.Property = Animatable.TransitionsProperty;
		Transitions transitions2 = new Transitions();
		BrushTransition brushTransition3 = new BrushTransition();
		brushTransition3.Property = TemplatedControl.ForegroundProperty;
		brushTransition3.Duration = TimeSpan.FromTicks(2000000L);
		transitions2.Add(brushTransition3);
		TransformOperationsTransition transformOperationsTransition2 = new TransformOperationsTransition();
		transformOperationsTransition2.Property = Visual.RenderTransformProperty;
		transformOperationsTransition2.Duration = TimeSpan.FromTicks(1500000L);
		transitions2.Add(transformOperationsTransition2);
		setter63.Value = transitions2;
		style24.Add(setter63);
		styles23.Add(style24);
		Styles styles24 = P_1.Styles;
		Style item10 = (style2 = new Style());
		context.PushParent(style2);
		Style style25 = style2;
		style25.Selector = ((Selector?)null).OfType(typeof(Button)).Class("link").Class(":pointerover");
		Setter setter64 = (setter5 = new Setter());
		context.PushParent(setter5);
		Setter setter65 = setter5;
		setter65.Property = Visual.RenderTransformProperty;
		setter65.Value = (ITransform)new TransformConverter().ConvertFrom(context, CultureInfo.InvariantCulture, "scale(1.05)");
		context.PopParent();
		style25.Add(setter64);
		context.PopParent();
		styles24.Add(item10);
		Styles styles25 = P_1.Styles;
		Style style26 = new Style();
		style26.Selector = ((Selector?)null).OfType(typeof(Button)).Class("link").Class(":pointerover")
			.Descendant()
			.OfType(typeof(PathIcon));
		Setter setter66 = new Setter();
		setter66.Property = TemplatedControl.ForegroundProperty;
		setter66.Value = new ImmutableSolidColorBrush(4282339765u);
		style26.Add(setter66);
		styles25.Add(style26);
		Styles styles26 = P_1.Styles;
		Style item11 = (style2 = new Style());
		context.PushParent(style2);
		Style style27 = style2;
		style27.Selector = ((Selector?)null).OfType(typeof(Button)).Class("link").Class(":pressed");
		Setter setter67 = (setter5 = new Setter());
		context.PushParent(setter5);
		Setter setter68 = setter5;
		setter68.Property = Visual.RenderTransformProperty;
		setter68.Value = (ITransform)new TransformConverter().ConvertFrom(context, CultureInfo.InvariantCulture, "scale(0.95)");
		context.PopParent();
		style27.Add(setter67);
		context.PopParent();
		styles26.Add(item11);
		Styles styles27 = P_1.Styles;
		Style style28 = new Style();
		style28.Selector = ((Selector?)null).OfType(typeof(Button)).Class("link").Class(":pressed")
			.Descendant()
			.OfType(typeof(TextBlock));
		Setter setter69 = new Setter();
		setter69.Property = TextBlock.ForegroundProperty;
		setter69.Value = new ImmutableSolidColorBrush(4281352095u);
		style28.Add(setter69);
		styles27.Add(style28);
		Styles styles28 = P_1.Styles;
		Style style29 = new Style();
		style29.Selector = ((Selector?)null).OfType(typeof(Button)).Class("link").Class(":pressed")
			.Descendant()
			.OfType(typeof(PathIcon));
		Setter setter70 = new Setter();
		setter70.Property = TemplatedControl.ForegroundProperty;
		setter70.Value = new ImmutableSolidColorBrush(4281352095u);
		style29.Add(setter70);
		styles28.Add(style29);
		StackPanel stackPanel2;
		StackPanel stackPanel = (stackPanel2 = new StackPanel());
		((ISupportInitialize)stackPanel).BeginInit();
		P_1.Content = stackPanel;
		StackPanel stackPanel4;
		StackPanel stackPanel3 = (stackPanel4 = stackPanel2);
		context.PushParent(stackPanel4);
		StackPanel stackPanel5 = stackPanel4;
		stackPanel5.Margin = new Thickness(15.0, 10.0, 15.0, 15.0);
		stackPanel5.HorizontalAlignment = HorizontalAlignment.Center;
		stackPanel5.VerticalAlignment = VerticalAlignment.Center;
		stackPanel5.Orientation = Orientation.Vertical;
		stackPanel5.Spacing = 5.0;
		Controls children = stackPanel5.Children;
		Border border2;
		Border border = (border2 = new Border());
		((ISupportInitialize)border).BeginInit();
		children.Add(border);
		Border border4;
		Border border3 = (border4 = border2);
		context.PushParent(border4);
		Border border5 = border4;
		border5.Width = 60.0;
		border5.Height = 60.0;
		border5.CornerRadius = new CornerRadius(30.0, 30.0, 30.0, 30.0);
		border5.ClipToBounds = true;
		border5.Margin = new Thickness(0.0, 15.0, 0.0, 5.0);
		border5.HorizontalAlignment = HorizontalAlignment.Center;
		DropShadowEffect dropShadowEffect3 = new DropShadowEffect();
		dropShadowEffect3.BlurRadius = 12.0;
		dropShadowEffect3.Opacity = 0.5;
		dropShadowEffect3.OffsetX = 0.0;
		dropShadowEffect3.OffsetY = 4.0;
		dropShadowEffect3.Color = Color.FromUInt32(4278190080u);
		border5.Effect = dropShadowEffect3;
		Image image2;
		Image image = (image2 = new Image());
		((ISupportInitialize)image).BeginInit();
		border5.Child = image;
		Image image4;
		Image image3 = (image4 = image2);
		context.PushParent(image4);
		image4.Source = (IImage)new BitmapTypeConverter().ConvertFrom(context, CultureInfo.InvariantCulture, "/Assets/织梦logo.png");
		image4.Stretch = Stretch.UniformToFill;
		image4.Margin = new Thickness(-5.0, -5.0, -5.0, -5.0);
		image4.Opacity = 0.9;
		context.PopParent();
		((ISupportInitialize)image3).EndInit();
		context.PopParent();
		((ISupportInitialize)border3).EndInit();
		Controls children2 = stackPanel5.Children;
		TextBlock textBlock2;
		TextBlock textBlock = (textBlock2 = new TextBlock());
		((ISupportInitialize)textBlock).BeginInit();
		children2.Add(textBlock);
		textBlock2.Margin = new Thickness(0.0, 0.0, 0.0, 10.0);
		textBlock2.HorizontalAlignment = HorizontalAlignment.Center;
		textBlock2.FontSize = 23.0;
		textBlock2.FontWeight = FontWeight.Bold;
		textBlock2.Foreground = new ImmutableSolidColorBrush(4282339765u);
		textBlock2.Text = "CursorPro免费使用";
		DropShadowEffect dropShadowEffect4 = new DropShadowEffect();
		dropShadowEffect4.BlurRadius = 4.0;
		dropShadowEffect4.Opacity = 0.3;
		dropShadowEffect4.OffsetX = 1.0;
		dropShadowEffect4.OffsetY = 2.0;
		dropShadowEffect4.Color = Color.FromUInt32(4286611584u);
		textBlock2.Effect = dropShadowEffect4;
		((ISupportInitialize)textBlock2).EndInit();
		Controls children3 = stackPanel5.Children;
		TextBlock textBlock4;
		TextBlock textBlock3 = (textBlock4 = new TextBlock());
		((ISupportInitialize)textBlock3).BeginInit();
		children3.Add(textBlock3);
		TextBlock textBlock6;
		TextBlock textBlock5 = (textBlock6 = textBlock4);
		context.PushParent(textBlock6);
		TextBlock textBlock7 = textBlock6;
		textBlock7.HorizontalAlignment = HorizontalAlignment.Center;
		textBlock7.TextAlignment = TextAlignment.Center;
		textBlock7.FontSize = 16.0;
		textBlock7.Foreground = new ImmutableSolidColorBrush(4283215696u);
		textBlock7.FontWeight = FontWeight.Bold;
		// StyledProperty<string?> textProperty = TextBlock.TextProperty;
		// CompiledBindingExtension compiledBindingExtension3 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E获取状态文本_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		// context.ProvideTargetProperty = TextBlock.TextProperty;
		// CompiledBindingExtension binding2 = compiledBindingExtension3.ProvideValue(context);
		// context.ProvideTargetProperty = null;
		// AvaloniaObjectExtensions.Bind(textBlock7, textProperty, (IBinding)binding2);
		textBlock7.TextWrapping = TextWrapping.Wrap;
		textBlock7.MinHeight = 20.0;
		textBlock7.Margin = new Thickness(0.0, 0.0, 0.0, 5.0);
		context.PopParent();
		((ISupportInitialize)textBlock5).EndInit();
		Controls children4 = stackPanel5.Children;
		Button button2;
		Button button = (button2 = new Button());
		((ISupportInitialize)button).BeginInit();
		children4.Add(button);
		Button button4;
		Button button3 = (button4 = button2);
		context.PushParent(button4);
		Button button5 = button4;
		StyledProperty<ICommand?> commandProperty = Button.CommandProperty;
		CompiledBindingExtension compiledBindingExtension4 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E状态文本点击Command_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = Button.CommandProperty;
		CompiledBindingExtension binding3 = compiledBindingExtension4.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(button5, commandProperty, (IBinding)binding3);
		button5.Background = new ImmutableSolidColorBrush(16777215u);
		button5.BorderThickness = new Thickness(0.0, 0.0, 0.0, 0.0);
		button5.Padding = new Thickness(0.0, 0.0, 0.0, 0.0);
		button5.Cursor = new Cursor(StandardCursorType.Hand);
		button5.HorizontalAlignment = HorizontalAlignment.Center;
		button5.Margin = new Thickness(0.0, 0.0, 0.0, 0.0);
		Styles styles29 = button5.Styles;
		Style style30 = new Style();
		style30.Selector = ((Selector?)null).OfType(typeof(Button));
		Setter setter71 = new Setter();
		setter71.Property = Animatable.TransitionsProperty;
		Transitions transitions3 = new Transitions();
		TransformOperationsTransition transformOperationsTransition3 = new TransformOperationsTransition();
		transformOperationsTransition3.Property = Visual.RenderTransformProperty;
		transformOperationsTransition3.Duration = TimeSpan.FromTicks(2000000L);
		transitions3.Add(transformOperationsTransition3);
		setter71.Value = transitions3;
		style30.Add(setter71);
		styles29.Add(style30);
		Styles styles30 = button5.Styles;
		Style item12 = (style2 = new Style());
		context.PushParent(style2);
		Style style31 = style2;
		style31.Selector = ((Selector?)null).OfType(typeof(Button)).Class(":pointerover");
		Setter setter72 = (setter5 = new Setter());
		context.PushParent(setter5);
		Setter setter73 = setter5;
		setter73.Property = Visual.RenderTransformProperty;
		setter73.Value = (ITransform)new TransformConverter().ConvertFrom(context, CultureInfo.InvariantCulture, "scale(1.05)");
		context.PopParent();
		style31.Add(setter72);
		context.PopParent();
		styles30.Add(item12);
		Styles styles31 = button5.Styles;
		Style item13 = (style2 = new Style());
		context.PushParent(style2);
		Style style32 = style2;
		style32.Selector = ((Selector?)null).OfType(typeof(Button)).Class(":pressed");
		Setter setter74 = (setter5 = new Setter());
		context.PushParent(setter5);
		Setter setter75 = setter5;
		setter75.Property = Visual.RenderTransformProperty;
		setter75.Value = (ITransform)new TransformConverter().ConvertFrom(context, CultureInfo.InvariantCulture, "scale(0.95)");
		context.PopParent();
		style32.Add(setter74);
		context.PopParent();
		styles31.Add(item13);
		Styles styles32 = button5.Styles;
		Style style33 = new Style();
		style33.Selector = ((Selector?)null).OfType(typeof(Button)).Class(":pointerover").Descendant()
			.OfType(typeof(Border));
		Setter setter76 = new Setter();
		setter76.Property = Border.BackgroundProperty;
		setter76.Value = new ImmutableSolidColorBrush(4294959344u);
		style33.Add(setter76);
		Setter setter77 = new Setter();
		setter77.Property = Visual.EffectProperty;
		DropShadowEffect dropShadowEffect5 = new DropShadowEffect();
		dropShadowEffect5.BlurRadius = 10.0;
		dropShadowEffect5.Opacity = 0.5;
		dropShadowEffect5.OffsetX = 0.0;
		dropShadowEffect5.OffsetY = 3.0;
		dropShadowEffect5.Color = Color.FromUInt32(4294928820u);
		setter77.Value = dropShadowEffect5;
		style33.Add(setter77);
		styles32.Add(style33);
		Styles styles33 = button5.Styles;
		Style style34 = new Style();
		style34.Selector = ((Selector?)null).OfType(typeof(Button)).Class(":pointerover").Descendant()
			.OfType(typeof(TextBlock));
		Setter setter78 = new Setter();
		setter78.Property = TextBlock.ForegroundProperty;
		setter78.Value = new ImmutableSolidColorBrush(4292352864u);
		style34.Add(setter78);
		styles33.Add(style34);
		Styles styles34 = button5.Styles;
		Style style35 = new Style();
		style35.Selector = ((Selector?)null).OfType(typeof(Button)).Class(":pressed").Descendant()
			.OfType(typeof(TextBlock));
		Setter setter79 = new Setter();
		setter79.Property = TextBlock.ForegroundProperty;
		setter79.Value = new ImmutableSolidColorBrush(4290910299u);
		style35.Add(setter79);
		styles34.Add(style35);
		StackPanel stackPanel7;
		StackPanel stackPanel6 = (stackPanel7 = new StackPanel());
		((ISupportInitialize)stackPanel6).BeginInit();
		button5.Content = stackPanel6;
		StackPanel stackPanel8 = (stackPanel4 = stackPanel7);
		context.PushParent(stackPanel4);
		StackPanel stackPanel9 = stackPanel4;
		stackPanel9.Orientation = Orientation.Horizontal;
		stackPanel9.HorizontalAlignment = HorizontalAlignment.Center;
		stackPanel9.Margin = new Thickness(0.0, 0.0, 0.0, 0.0);
		Controls children5 = stackPanel9.Children;
		TextBlock textBlock9;
		TextBlock textBlock8 = (textBlock9 = new TextBlock());
		((ISupportInitialize)textBlock8).BeginInit();
		children5.Add(textBlock8);
		textBlock9.Text = "✦";
		textBlock9.FontSize = 15.0;
		textBlock9.Foreground = new ImmutableSolidColorBrush(4294928820u);
		textBlock9.FontWeight = FontWeight.Bold;
		textBlock9.Margin = new Thickness(0.0, 0.0, 8.0, 0.0);
		textBlock9.VerticalAlignment = VerticalAlignment.Center;
		((ISupportInitialize)textBlock9).EndInit();
		Controls children6 = stackPanel9.Children;
		Border border7;
		Border border6 = (border7 = new Border());
		((ISupportInitialize)border6).BeginInit();
		children6.Add(border6);
		Border border8 = (border4 = border7);
		context.PushParent(border4);
		Border border9 = border4;
		border9.Background = new ImmutableSolidColorBrush(4294964728u);
		border9.CornerRadius = new CornerRadius(10.0, 10.0, 10.0, 10.0);
		border9.Padding = new Thickness(8.0, 3.0, 8.0, 3.0);
		Transitions transitions4 = new Transitions();
		BrushTransition brushTransition4 = new BrushTransition();
		brushTransition4.Property = Border.BackgroundProperty;
		brushTransition4.Duration = TimeSpan.FromTicks(2000000L);
		transitions4.Add(brushTransition4);
		EffectTransition effectTransition = new EffectTransition();
		effectTransition.Property = Visual.EffectProperty;
		effectTransition.Duration = TimeSpan.FromTicks(2000000L);
		transitions4.Add(effectTransition);
		border9.Transitions = transitions4;
		DropShadowEffect dropShadowEffect6 = new DropShadowEffect();
		dropShadowEffect6.BlurRadius = 5.0;
		dropShadowEffect6.Opacity = 0.2;
		dropShadowEffect6.OffsetX = 0.0;
		dropShadowEffect6.OffsetY = 1.0;
		dropShadowEffect6.Color = Color.FromUInt32(4294928820u);
		border9.Effect = dropShadowEffect6;
		TextBlock textBlock11;
		TextBlock textBlock10 = (textBlock11 = new TextBlock());
		((ISupportInitialize)textBlock10).BeginInit();
		border9.Child = textBlock10;
		TextBlock textBlock12 = (textBlock6 = textBlock11);
		context.PushParent(textBlock6);
		TextBlock textBlock13 = textBlock6;
		textBlock13.HorizontalAlignment = HorizontalAlignment.Center;
		textBlock13.FontSize = 14.0;
		textBlock13.Foreground = new ImmutableSolidColorBrush(4294928820u);
		textBlock13.FontWeight = FontWeight.Bold;
		StyledProperty<string?> textProperty2 = TextBlock.TextProperty;
		CompiledBindingExtension compiledBindingExtension5 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E使用类型文本_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = TextBlock.TextProperty;
		CompiledBindingExtension binding4 = compiledBindingExtension5.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(textBlock13, textProperty2, (IBinding)binding4);
		textBlock13.MinHeight = 20.0;
		Transitions transitions5 = new Transitions();
		BrushTransition brushTransition5 = new BrushTransition();
		brushTransition5.Property = TextBlock.ForegroundProperty;
		brushTransition5.Duration = TimeSpan.FromTicks(2000000L);
		transitions5.Add(brushTransition5);
		textBlock13.Transitions = transitions5;
		context.PopParent();
		((ISupportInitialize)textBlock12).EndInit();
		context.PopParent();
		((ISupportInitialize)border8).EndInit();
		Controls children7 = stackPanel9.Children;
		TextBlock textBlock15;
		TextBlock textBlock14 = (textBlock15 = new TextBlock());
		((ISupportInitialize)textBlock14).BeginInit();
		children7.Add(textBlock14);
		textBlock15.Text = "✦";
		textBlock15.FontSize = 15.0;
		textBlock15.Foreground = new ImmutableSolidColorBrush(4294928820u);
		textBlock15.FontWeight = FontWeight.Bold;
		textBlock15.Margin = new Thickness(8.0, 0.0, 0.0, 0.0);
		textBlock15.VerticalAlignment = VerticalAlignment.Center;
		((ISupportInitialize)textBlock15).EndInit();
		context.PopParent();
		((ISupportInitialize)stackPanel8).EndInit();
		context.PopParent();
		((ISupportInitialize)button3).EndInit();
		Controls children8 = stackPanel5.Children;
		Border border11;
		Border border10 = (border11 = new Border());
		((ISupportInitialize)border10).BeginInit();
		children8.Add(border10);
		Border border12 = (border4 = border11);
		context.PushParent(border4);
		Border border13 = border4;
		border13.Width = 280.0;
		border13.Margin = new Thickness(0.0, 5.0, 0.0, 0.0);
		border13.HorizontalAlignment = HorizontalAlignment.Center;
		StackPanel stackPanel11;
		StackPanel stackPanel10 = (stackPanel11 = new StackPanel());
		((ISupportInitialize)stackPanel10).BeginInit();
		border13.Child = stackPanel10;
		StackPanel stackPanel12 = (stackPanel4 = stackPanel11);
		context.PushParent(stackPanel4);
		StackPanel stackPanel13 = stackPanel4;
		stackPanel13.Spacing = 12.0;
		Controls children9 = stackPanel13.Children;
		Button button7;
		Button button6 = (button7 = new Button());
		((ISupportInitialize)button6).BeginInit();
		children9.Add(button6);
		Button button8 = (button4 = button7);
		context.PushParent(button4);
		Button button9 = button4;
		button9.Name = "SwitchAccountButton";
		object element = button9;
		context.AvaloniaNameScope.Register("SwitchAccountButton", element);
		button9.Width = 200.0;
		button9.Height = 42.0;
		button9.HorizontalAlignment = HorizontalAlignment.Center;
		button9.HorizontalContentAlignment = HorizontalAlignment.Center;
		button9.VerticalContentAlignment = VerticalAlignment.Center;
		StyledProperty<ICommand?> commandProperty2 = Button.CommandProperty;
		CompiledBindingExtension compiledBindingExtension6 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E一键获取快速额度Command_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = Button.CommandProperty;
		CompiledBindingExtension binding5 = compiledBindingExtension6.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(button9, commandProperty2, (IBinding)binding5);
		button9.Content = "一键获取额度";
		button9.FontSize = 15.0;
		button9.Background = new ImmutableSolidColorBrush(4282339765u);
		button9.Foreground = new ImmutableSolidColorBrush(uint.MaxValue);
		button9.BorderThickness = new Thickness(0.0, 0.0, 0.0, 0.0);
		button9.CornerRadius = new CornerRadius(8.0, 8.0, 8.0, 8.0);
		Styles styles35 = button9.Styles;
		Style item14 = (style2 = new Style());
		context.PushParent(style2);
		Style style36 = style2;
		style36.Selector = ((Selector?)null).OfType(typeof(Button)).Class(":pointerover");
		Setter setter80 = (setter5 = new Setter());
		context.PushParent(setter5);
		Setter setter81 = setter5;
		setter81.Property = Visual.RenderTransformProperty;
		setter81.Value = (ITransform)new TransformConverter().ConvertFrom(context, CultureInfo.InvariantCulture, "scale(1.05)");
		context.PopParent();
		style36.Add(setter80);
		Setter setter82 = new Setter();
		setter82.Property = TemplatedControl.BackgroundProperty;
		setter82.Value = new ImmutableSolidColorBrush(4281352095u);
		style36.Add(setter82);
		Setter setter83 = new Setter();
		setter83.Property = TemplatedControl.ForegroundProperty;
		setter83.Value = new ImmutableSolidColorBrush(uint.MaxValue);
		style36.Add(setter83);
		context.PopParent();
		styles35.Add(item14);
		Styles styles36 = button9.Styles;
		Style item15 = (style2 = new Style());
		context.PushParent(style2);
		Style style37 = style2;
		style37.Selector = ((Selector?)null).OfType(typeof(Button)).Class(":pressed");
		Setter setter84 = (setter5 = new Setter());
		context.PushParent(setter5);
		Setter setter85 = setter5;
		setter85.Property = Visual.RenderTransformProperty;
		setter85.Value = (ITransform)new TransformConverter().ConvertFrom(context, CultureInfo.InvariantCulture, "scale(0.95)");
		context.PopParent();
		style37.Add(setter84);
		Setter setter86 = new Setter();
		setter86.Property = TemplatedControl.ForegroundProperty;
		setter86.Value = new ImmutableSolidColorBrush(uint.MaxValue);
		style37.Add(setter86);
		context.PopParent();
		styles36.Add(item15);
		Styles styles37 = button9.Styles;
		Style style38 = new Style();
		style38.Selector = ((Selector?)null).OfType(typeof(Button)).Class(":pointerover");
		Setter setter87 = new Setter();
		setter87.Property = Visual.EffectProperty;
		DropShadowEffect dropShadowEffect7 = new DropShadowEffect();
		dropShadowEffect7.BlurRadius = 8.0;
		dropShadowEffect7.Opacity = 0.3;
		dropShadowEffect7.OffsetX = 0.0;
		dropShadowEffect7.OffsetY = 2.0;
		setter87.Value = dropShadowEffect7;
		style38.Add(setter87);
		styles37.Add(style38);
		context.PopParent();
		((ISupportInitialize)button8).EndInit();
		Controls children10 = stackPanel13.Children;
		Button button11;
		Button button10 = (button11 = new Button());
		((ISupportInitialize)button10).BeginInit();
		children10.Add(button10);
		Button button12 = (button4 = button11);
		context.PushParent(button4);
		Button button13 = button4;
		button13.Name = "TutorialButton";
		element = button13;
		context.AvaloniaNameScope.Register("TutorialButton", element);
		button13.Width = 200.0;
		button13.Height = 42.0;
		button13.HorizontalAlignment = HorizontalAlignment.Center;
		button13.HorizontalContentAlignment = HorizontalAlignment.Center;
		button13.VerticalContentAlignment = VerticalAlignment.Center;
		StyledProperty<ICommand?> commandProperty3 = Button.CommandProperty;
		CompiledBindingExtension compiledBindingExtension7 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E使用教程Command_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = Button.CommandProperty;
		CompiledBindingExtension binding6 = compiledBindingExtension7.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(button13, commandProperty3, (IBinding)binding6);
		button13.Content = "使用教程";
		button13.FontSize = 15.0;
		button13.Background = new ImmutableSolidColorBrush(4290406600u);
		button13.Foreground = new ImmutableSolidColorBrush(uint.MaxValue);
		button13.BorderThickness = new Thickness(0.0, 0.0, 0.0, 0.0);
		button13.CornerRadius = new CornerRadius(8.0, 8.0, 8.0, 8.0);
		Styles styles38 = button13.Styles;
		Style item16 = (style2 = new Style());
		context.PushParent(style2);
		Style style39 = style2;
		style39.Selector = ((Selector?)null).OfType(typeof(Button)).Class(":pointerover");
		Setter setter88 = (setter5 = new Setter());
		context.PushParent(setter5);
		Setter setter89 = setter5;
		setter89.Property = Visual.RenderTransformProperty;
		setter89.Value = (ITransform)new TransformConverter().ConvertFrom(context, CultureInfo.InvariantCulture, "scale(1.05)");
		context.PopParent();
		style39.Add(setter88);
		Setter setter90 = new Setter();
		setter90.Property = TemplatedControl.BackgroundProperty;
		setter90.Value = new ImmutableSolidColorBrush(4288423856u);
		style39.Add(setter90);
		Setter setter91 = new Setter();
		setter91.Property = TemplatedControl.ForegroundProperty;
		setter91.Value = new ImmutableSolidColorBrush(uint.MaxValue);
		style39.Add(setter91);
		context.PopParent();
		styles38.Add(item16);
		Styles styles39 = button13.Styles;
		Style item17 = (style2 = new Style());
		context.PushParent(style2);
		Style style40 = style2;
		style40.Selector = ((Selector?)null).OfType(typeof(Button)).Class(":pressed");
		Setter setter92 = (setter5 = new Setter());
		context.PushParent(setter5);
		Setter setter93 = setter5;
		setter93.Property = Visual.RenderTransformProperty;
		setter93.Value = (ITransform)new TransformConverter().ConvertFrom(context, CultureInfo.InvariantCulture, "scale(0.95)");
		context.PopParent();
		style40.Add(setter92);
		Setter setter94 = new Setter();
		setter94.Property = TemplatedControl.ForegroundProperty;
		setter94.Value = new ImmutableSolidColorBrush(uint.MaxValue);
		style40.Add(setter94);
		context.PopParent();
		styles39.Add(item17);
		Styles styles40 = button13.Styles;
		Style style41 = new Style();
		style41.Selector = ((Selector?)null).OfType(typeof(Button)).Class(":pointerover");
		Setter setter95 = new Setter();
		setter95.Property = Visual.EffectProperty;
		DropShadowEffect dropShadowEffect8 = new DropShadowEffect();
		dropShadowEffect8.BlurRadius = 8.0;
		dropShadowEffect8.Opacity = 0.3;
		dropShadowEffect8.OffsetX = 0.0;
		dropShadowEffect8.OffsetY = 2.0;
		setter95.Value = dropShadowEffect8;
		style41.Add(setter95);
		styles40.Add(style41);
		context.PopParent();
		((ISupportInitialize)button12).EndInit();
		Controls children11 = stackPanel13.Children;
		CheckBox checkBox2;
		CheckBox checkBox = (checkBox2 = new CheckBox());
		((ISupportInitialize)checkBox).BeginInit();
		children11.Add(checkBox);
		CheckBox checkBox4;
		CheckBox checkBox3 = (checkBox4 = checkBox2);
		context.PushParent(checkBox4);
		checkBox4.Name = "AccelerateCursorCheckBox";
		element = checkBox4;
		context.AvaloniaNameScope.Register("AccelerateCursorCheckBox", element);
		checkBox4.HorizontalAlignment = HorizontalAlignment.Center;
		checkBox4.Margin = new Thickness(0.0, -7.0, 0.0, 5.0);
		StyledProperty<bool> isVisibleProperty = Visual.IsVisibleProperty;
		CompiledBindingExtension compiledBindingExtension8 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E显示加速Cursor功能_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = Visual.IsVisibleProperty;
		CompiledBindingExtension binding7 = compiledBindingExtension8.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(checkBox4, isVisibleProperty, (IBinding)binding7);
		StyledProperty<bool?> isCheckedProperty = ToggleButton.IsCheckedProperty;
		CompiledBindingExtension compiledBindingExtension9 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E加速Cursor已启用_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = ToggleButton.IsCheckedProperty;
		CompiledBindingExtension binding8 = compiledBindingExtension9.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(checkBox4, isCheckedProperty, (IBinding)binding8);
		StyledProperty<ICommand?> commandProperty4 = Button.CommandProperty;
		CompiledBindingExtension compiledBindingExtension10 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E加速Cursor切换Command_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = Button.CommandProperty;
		CompiledBindingExtension binding9 = compiledBindingExtension10.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(checkBox4, commandProperty4, (IBinding)binding9);
		checkBox4.Width = 120.0;
		checkBox4.Height = 22.0;
		Styles styles41 = checkBox4.Styles;
		Style style42 = new Style();
		style42.Selector = ((Selector?)null).OfType(typeof(CheckBox));
		Setter setter96 = new Setter();
		setter96.Property = InputElement.CursorProperty;
		setter96.Value = new Cursor(StandardCursorType.Hand);
		style42.Add(setter96);
		Setter setter97 = new Setter();
		setter97.Property = Animatable.TransitionsProperty;
		Transitions transitions6 = new Transitions();
		TransformOperationsTransition transformOperationsTransition4 = new TransformOperationsTransition();
		transformOperationsTransition4.Property = Visual.RenderTransformProperty;
		transformOperationsTransition4.Duration = TimeSpan.FromTicks(1500000L);
		transitions6.Add(transformOperationsTransition4);
		setter97.Value = transitions6;
		style42.Add(setter97);
		styles41.Add(style42);
		Styles styles42 = checkBox4.Styles;
		Style item18 = (style2 = new Style());
		context.PushParent(style2);
		Style style43 = style2;
		style43.Selector = ((Selector?)null).OfType(typeof(CheckBox)).Class(":pointerover");
		Setter setter98 = (setter5 = new Setter());
		context.PushParent(setter5);
		Setter setter99 = setter5;
		setter99.Property = Visual.RenderTransformProperty;
		setter99.Value = (ITransform)new TransformConverter().ConvertFrom(context, CultureInfo.InvariantCulture, "scale(1.02)");
		context.PopParent();
		style43.Add(setter98);
		context.PopParent();
		styles42.Add(item18);
		Styles styles43 = checkBox4.Styles;
		Style style44 = new Style();
		style44.Selector = ((Selector?)null).OfType(typeof(CheckBox)).Class(":pointerover").Descendant()
			.OfType(typeof(TextBlock));
		Setter setter100 = new Setter();
		setter100.Property = TextBlock.ForegroundProperty;
		setter100.Value = new ImmutableSolidColorBrush(4294929205u);
		style44.Add(setter100);
		styles43.Add(style44);
		Styles styles44 = checkBox4.Styles;
		Style style45 = new Style();
		style45.Selector = ((Selector?)null).OfType(typeof(CheckBox)).Class(":checked").Descendant()
			.OfType(typeof(TextBlock));
		Setter setter101 = new Setter();
		setter101.Property = TextBlock.ForegroundProperty;
		setter101.Value = new ImmutableSolidColorBrush(4283215696u);
		style45.Add(setter101);
		Setter setter102 = new Setter();
		setter102.Property = TextBlock.FontWeightProperty;
		setter102.Value = FontWeight.Bold;
		style45.Add(setter102);
		styles44.Add(style45);
		TextBlock textBlock17;
		TextBlock textBlock16 = (textBlock17 = new TextBlock());
		((ISupportInitialize)textBlock16).BeginInit();
		checkBox4.Content = textBlock16;
		textBlock17.Text = "突破锁区限制";
		textBlock17.FontSize = 13.0;
		textBlock17.Foreground = new ImmutableSolidColorBrush(4284900966u);
		textBlock17.VerticalAlignment = VerticalAlignment.Center;
		((ISupportInitialize)textBlock17).EndInit();
		context.PopParent();
		((ISupportInitialize)checkBox3).EndInit();
		Controls children12 = stackPanel13.Children;
		Button button15;
		Button button14 = (button15 = new Button());
		((ISupportInitialize)button14).BeginInit();
		children12.Add(button14);
		Button button16 = (button4 = button15);
		context.PushParent(button4);
		Button button17 = button4;
		button17.HorizontalAlignment = HorizontalAlignment.Center;
		StyledProperty<ICommand?> commandProperty5 = Button.CommandProperty;
		CompiledBindingExtension compiledBindingExtension11 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E版本链接Command_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = Button.CommandProperty;
		CompiledBindingExtension binding10 = compiledBindingExtension11.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(button17, commandProperty5, (IBinding)binding10);
		button17.Classes.Add("link");
		button17.Background = new ImmutableSolidColorBrush(16777215u);
		button17.BorderThickness = new Thickness(0.0, 0.0, 0.0, 0.0);
		button17.Padding = new Thickness(5.0, 2.0, 5.0, 2.0);
		StyledProperty<Thickness> marginProperty = Layoutable.MarginProperty;
		CompiledBindingExtension compiledBindingExtension12 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E版本链接边距_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = Layoutable.MarginProperty;
		CompiledBindingExtension binding11 = compiledBindingExtension12.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(button17, marginProperty, (IBinding)binding11);
		StackPanel stackPanel15;
		StackPanel stackPanel14 = (stackPanel15 = new StackPanel());
		((ISupportInitialize)stackPanel14).BeginInit();
		button17.Content = stackPanel14;
		StackPanel stackPanel16 = (stackPanel4 = stackPanel15);
		context.PushParent(stackPanel4);
		StackPanel stackPanel17 = stackPanel4;
		stackPanel17.Orientation = Orientation.Horizontal;
		stackPanel17.Spacing = 5.0;
		Controls children13 = stackPanel17.Children;
		Canvas canvas2;
		Canvas canvas = (canvas2 = new Canvas());
		((ISupportInitialize)canvas).BeginInit();
		children13.Add(canvas);
		canvas2.Width = 12.0;
		canvas2.Height = 12.0;
		canvas2.VerticalAlignment = VerticalAlignment.Center;
		Controls children14 = canvas2.Children;
		Rectangle rectangle2;
		Rectangle rectangle = (rectangle2 = new Rectangle());
		((ISupportInitialize)rectangle).BeginInit();
		children14.Add(rectangle);
		rectangle2.Fill = new ImmutableSolidColorBrush(4285887861u);
		rectangle2.Width = 1.0;
		rectangle2.Height = 7.0;
		Canvas.SetLeft(rectangle2, 5.5);
		Canvas.SetTop(rectangle2, 1.0);
		((ISupportInitialize)rectangle2).EndInit();
		Controls children15 = canvas2.Children;
		Polygon polygon2;
		Polygon polygon = (polygon2 = new Polygon());
		((ISupportInitialize)polygon).BeginInit();
		children15.Add(polygon);
		polygon2.Fill = new ImmutableSolidColorBrush(4285887861u);
		polygon2.Points = new Point[3]
		{
			new Point(6.0, 8.0),
			new Point(3.0, 5.0),
			new Point(9.0, 5.0)
		};
		((ISupportInitialize)polygon2).EndInit();
		Controls children16 = canvas2.Children;
		Rectangle rectangle4;
		Rectangle rectangle3 = (rectangle4 = new Rectangle());
		((ISupportInitialize)rectangle3).BeginInit();
		children16.Add(rectangle3);
		rectangle4.Fill = new ImmutableSolidColorBrush(4285887861u);
		rectangle4.Width = 8.0;
		rectangle4.Height = 1.0;
		Canvas.SetLeft(rectangle4, 2.0);
		Canvas.SetTop(rectangle4, 10.0);
		((ISupportInitialize)rectangle4).EndInit();
		((ISupportInitialize)canvas2).EndInit();
		Controls children17 = stackPanel17.Children;
		TextBlock textBlock19;
		TextBlock textBlock18 = (textBlock19 = new TextBlock());
		((ISupportInitialize)textBlock18).BeginInit();
		children17.Add(textBlock18);
		TextBlock textBlock20 = (textBlock6 = textBlock19);
		context.PushParent(textBlock6);
		TextBlock textBlock21 = textBlock6;
		StyledProperty<string?> textProperty3 = TextBlock.TextProperty;
		CompiledBindingExtension compiledBindingExtension13 = new CompiledBindingExtension(new CompiledBindingPathBuilder(1).Property(CompiledAvaloniaXaml.XamlIlHelpers.CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E版本号_0021Property(), PropertyInfoAccessorFactory.CreateInpcPropertyAccessor).Build());
		context.ProvideTargetProperty = TextBlock.TextProperty;
		CompiledBindingExtension binding12 = compiledBindingExtension13.ProvideValue(context);
		context.ProvideTargetProperty = null;
		AvaloniaObjectExtensions.Bind(textBlock21, textProperty3, (IBinding)binding12);
		textBlock21.FontSize = 15.0;
		textBlock21.Foreground = new ImmutableSolidColorBrush(4285887861u);
		context.PopParent();
		((ISupportInitialize)textBlock20).EndInit();
		context.PopParent();
		((ISupportInitialize)stackPanel16).EndInit();
		context.PopParent();
		((ISupportInitialize)button16).EndInit();
		context.PopParent();
		((ISupportInitialize)stackPanel12).EndInit();
		context.PopParent();
		((ISupportInitialize)border12).EndInit();
		context.PopParent();
		((ISupportInitialize)stackPanel3).EndInit();
		context.PopParent();
		((ISupportInitialize)P_1).EndInit();
		if (P_1 is StyledElement styled)
		{
			NameScope.SetNameScope(styled, context.AvaloniaNameScope);
		}
		context.AvaloniaNameScope.Complete();
	}

	private static void _0021XamlIlPopulateTrampoline(主要窗口 P_0)
	{
		if (_0021XamlIlPopulateOverride != null)
		{
			_0021XamlIlPopulateOverride(P_0);
		}
		else
		{
			_0021XamlIlPopulate(XamlIlRuntimeHelpers.CreateRootServiceProviderV3(null), P_0);
		}
	}
}
