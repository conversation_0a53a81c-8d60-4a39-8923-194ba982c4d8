using System;
using System.Diagnostics;
using System.Threading;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Controls.ApplicationLifetimes;

namespace CursorPro.ViewModels;

public class Mutex
{
	private static System.Threading.Mutex? _mutex;

	private static readonly string _mutexName = "CursorPro_SingleInstance";

	public static bool Initialize()
	{
		try
		{
			_mutex = new System.Threading.Mutex(initiallyOwned: true, _mutexName, out var createdNew);
			if (!createdNew)
			{
				ActivateExistingWindow();
				if (Application.Current?.ApplicationLifetime is IClassicDesktopStyleApplicationLifetime classicDesktopStyleApplicationLifetime)
				{
					classicDesktopStyleApplicationLifetime.Shutdown();
				}
				return false;
			}
			if (Application.Current?.ApplicationLifetime is IClassicDesktopStyleApplicationLifetime classicDesktopStyleApplicationLifetime2)
			{
				classicDesktopStyleApplicationLifetime2.Exit += delegate
				{
					_mutex?.ReleaseMutex();
					_mutex?.Dispose();
				};
			}
			return true;
		}
		catch (Exception)
		{
			return false;
		}
	}

	private static Window? GetMainWindow()
	{
		if (Application.Current?.ApplicationLifetime is IClassicDesktopStyleApplicationLifetime classicDesktopStyleApplicationLifetime)
		{
			return classicDesktopStyleApplicationLifetime.MainWindow;
		}
		return null;
	}

	private static void ActivateExistingWindow()
	{
		try
		{
			Process currentProcess = Process.GetCurrentProcess();
			Process[] processesByName = Process.GetProcessesByName(currentProcess.ProcessName);
			foreach (Process process in processesByName)
			{
				if (process.Id != currentProcess.Id)
				{
					if (OperatingSystem.IsWindows())
					{
						WindowsNativeMethods.SetForegroundWindow(process.MainWindowHandle);
					}
					else if (OperatingSystem.IsMacOS())
					{
						ActivateMacOSWindow(currentProcess.ProcessName);
					}
					break;
				}
			}
		}
		catch (Exception)
		{
		}
	}

	private static void ActivateMacOSWindow(string processName)
	{
		try
		{
			string text = processName;
			if (text.EndsWith(".exe"))
			{
				text = text.Substring(0, text.Length - 4);
			}
			string text2 = "tell application \"System Events\"\n    set frontmost of every process whose name contains \"" + text + "\" to true\nend tell";
			using Process process = Process.Start(new ProcessStartInfo
			{
				FileName = "/usr/bin/osascript",
				Arguments = "-e \"" + text2 + "\"",
				UseShellExecute = false,
				CreateNoWindow = true
			});
			process?.WaitForExit(1000);
		}
		catch (Exception)
		{
		}
	}
}
