using System;
using System.Linq;
using System.Runtime.CompilerServices;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Data.Core.Plugins;
using Avalonia.Markup.Xaml.XamlIl.Runtime;
using Avalonia.Styling;
using Avalonia.Themes.Fluent;
using CompiledAvaloniaXaml;
using CursorPro.ViewModels;
// using CursorPro.Views; // 暂时注释掉Views引用

namespace CursorPro;

[CompilerGenerated]
public class App : Application
{
	private static Action<object> _0021XamlIlPopulateOverride;

	public override void Initialize()
	{
		_0021XamlIlPopulateTrampoline(this);
	}

	public override void OnFrameworkInitializationCompleted()
	{
		if (base.ApplicationLifetime is IClassicDesktopStyleApplicationLifetime classicDesktopStyleApplicationLifetime)
		{
			DisableAvaloniaDataAnnotationValidation();
			if (!Mutex.Initialize())
			{
				return;
			}
			// 暂时注释掉View相关代码
			// classicDesktopStyleApplicationLifetime.MainWindow = new CursorPro.Views.主要窗口
			// {
			// 	DataContext = new CursorPro.ViewModels.主要窗口()
			// };
			// classicDesktopStyleApplicationLifetime.MainWindow.Show();
			classicDesktopStyleApplicationLifetime.ShutdownRequested += Desktop_ShutdownRequested;
		}
		base.OnFrameworkInitializationCompleted();
	}

	private void Desktop_ShutdownRequested(object? sender, ShutdownRequestedEventArgs e)
	{
		if (sender is IClassicDesktopStyleApplicationLifetime classicDesktopStyleApplicationLifetime && classicDesktopStyleApplicationLifetime.MainWindow?.DataContext is IDisposable disposable)
		{
			try
			{
				disposable.Dispose();
			}
			catch (Exception)
			{
			}
		}
	}

	private void DisableAvaloniaDataAnnotationValidation()
	{
		DataAnnotationsValidationPlugin[] array = BindingPlugins.DataValidators.OfType<DataAnnotationsValidationPlugin>().ToArray();
		foreach (DataAnnotationsValidationPlugin item in array)
		{
			BindingPlugins.DataValidators.Remove(item);
		}
	}

	private static void _0021XamlIlPopulate(IServiceProvider P_0, App P_1)
	{
		CompiledAvaloniaXaml.XamlIlContext.Context<App> context = new CompiledAvaloniaXaml.XamlIlContext.Context<App>(P_0, new object[1] { _0021AvaloniaResources.NamespaceInfo_003A_002FApp_002Eaxaml.Singleton }, "avares://CursorPro/App.axaml")
		{
			RootObject = P_1,
			IntermediateRoot = P_1
		};
		App app2;
		App app = (app2 = P_1);
		context.PushParent(app2);
		app2.RequestedThemeVariant = ThemeVariant.Light;
		app2.DataTemplates.Add(new ViewLocator());
		app2.Styles.Add(new FluentTheme(context));
		context.PopParent();
		// 暂时注释掉有问题的NameScope设置
		// if (app is Application appElement)
		// {
		// 	NameScope.SetNameScope(appElement, context.AvaloniaNameScope);
		// }
		context.AvaloniaNameScope.Complete();
	}

	private static void _0021XamlIlPopulateTrampoline(App P_0)
	{
		if (_0021XamlIlPopulateOverride != null)
		{
			_0021XamlIlPopulateOverride(P_0);
		}
		else
		{
			_0021XamlIlPopulate(XamlIlRuntimeHelpers.CreateRootServiceProviderV3(null), P_0);
		}
	}
}
