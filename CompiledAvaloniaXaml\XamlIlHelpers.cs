using System.Runtime.CompilerServices;
using Avalonia;
using Avalonia.Data.Core;
using Avalonia.Styling;
using CommunityToolkit.Mvvm.Input;
using CursorPro.ViewModels;

namespace CompiledAvaloniaXaml;

[CompilerGenerated]
internal class XamlIlHelpers
{
	private static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E选择卡密Command_0021Field;

	private static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡有特惠_0021Field;

	private static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡原价_0021Field;

	private static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡现价_0021Field;

	private static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E价格加载中_0021Field;

	private static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡有特惠_0021Field;

	private static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡原价_0021Field;

	private static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡现价_0021Field;

	private static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E调整周卡数量Command_0021Field;

	private static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡数量_0021Field;

	private static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E调整月卡数量Command_0021Field;

	private static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡数量_0021Field;

	private static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E恢复购买记录Command_0021Field;

	private static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E显示恢复购买按钮_0021Field;

	private static IPropertyInfo CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E窗口标题_0021Field;

	private static IPropertyInfo Avalonia_002EStyling_002ESetter_002CAvalonia_002EBase_002EValue_0021Field;

	private static IPropertyInfo CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E获取状态文本_0021Field;

	private static IPropertyInfo CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E状态文本点击Command_0021Field;

	private static IPropertyInfo CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E使用类型文本_0021Field;

	private static IPropertyInfo CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E一键获取快速额度Command_0021Field;

	private static IPropertyInfo CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E使用教程Command_0021Field;

	private static IPropertyInfo CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E显示加速Cursor功能_0021Field;

	private static IPropertyInfo CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E加速Cursor已启用_0021Field;

	private static IPropertyInfo CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E加速Cursor切换Command_0021Field;

	private static IPropertyInfo CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E版本链接Command_0021Field;

	private static IPropertyInfo CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E版本链接边距_0021Field;

	private static IPropertyInfo CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E版本号_0021Field;

	private static object CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E选择卡密Command_0021Getter(object P_0)
	{
		return ((购买卡密VM)P_0).选择卡密Command;
	}

	public static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E选择卡密Command_0021Property()
	{
		if (CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E选择卡密Command_0021Field != null)
		{
			return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E选择卡密Command_0021Field;
		}
		CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E选择卡密Command_0021Field = new ClrPropertyInfo("选择卡密Command", (object P_0) => ((购买卡密VM)P_0).选择卡密Command, null, typeof(IAsyncRelayCommand<string>));
		return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E选择卡密Command_0021Field;
	}

	private static object CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡有特惠_0021Getter(object P_0)
	{
		return ((购买卡密VM)P_0).周卡有特惠;
	}

	public static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡有特惠_0021Property()
	{
		if (CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡有特惠_0021Field != null)
		{
			return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡有特惠_0021Field;
		}
		CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡有特惠_0021Field = new ClrPropertyInfo("周卡有特惠", (object P_0) => ((购买卡密VM)P_0).周卡有特惠, null, typeof(bool));
		return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡有特惠_0021Field;
	}

	private static object CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡原价_0021Getter(object P_0)
	{
		return ((购买卡密VM)P_0).周卡原价;
	}

	private static void CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡原价_0021Setter(object P_0, object P_1)
	{
		((购买卡密VM)P_0).周卡原价 = (string)P_1;
	}

	public static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡原价_0021Property()
	{
		if (CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡原价_0021Field != null)
		{
			return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡原价_0021Field;
		}
		CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡原价_0021Field = new ClrPropertyInfo("周卡原价", (object P_0) => ((购买卡密VM)P_0).周卡原价, delegate(object P_0, object P_1)
		{
			((购买卡密VM)P_0).周卡原价 = (string)P_1;
		}, typeof(string));
		return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡原价_0021Field;
	}

	private static object CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡现价_0021Getter(object P_0)
	{
		return ((购买卡密VM)P_0).周卡现价;
	}

	private static void CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡现价_0021Setter(object P_0, object P_1)
	{
		((购买卡密VM)P_0).周卡现价 = (string)P_1;
	}

	public static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡现价_0021Property()
	{
		if (CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡现价_0021Field != null)
		{
			return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡现价_0021Field;
		}
		CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡现价_0021Field = new ClrPropertyInfo("周卡现价", (object P_0) => ((购买卡密VM)P_0).周卡现价, delegate(object P_0, object P_1)
		{
			((购买卡密VM)P_0).周卡现价 = (string)P_1;
		}, typeof(string));
		return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡现价_0021Field;
	}

	private static object CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E价格加载中_0021Getter(object P_0)
	{
		return ((购买卡密VM)P_0).价格加载中;
	}

	private static void CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E价格加载中_0021Setter(object P_0, object P_1)
	{
		((购买卡密VM)P_0).价格加载中 = (bool)P_1;
	}

	public static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E价格加载中_0021Property()
	{
		if (CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E价格加载中_0021Field != null)
		{
			return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E价格加载中_0021Field;
		}
		CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E价格加载中_0021Field = new ClrPropertyInfo("价格加载中", (object P_0) => ((购买卡密VM)P_0).价格加载中, delegate(object P_0, object P_1)
		{
			((购买卡密VM)P_0).价格加载中 = (bool)P_1;
		}, typeof(bool));
		return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E价格加载中_0021Field;
	}

	private static object CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡有特惠_0021Getter(object P_0)
	{
		return ((购买卡密VM)P_0).月卡有特惠;
	}

	public static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡有特惠_0021Property()
	{
		if (CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡有特惠_0021Field != null)
		{
			return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡有特惠_0021Field;
		}
		CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡有特惠_0021Field = new ClrPropertyInfo("月卡有特惠", (object P_0) => ((购买卡密VM)P_0).月卡有特惠, null, typeof(bool));
		return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡有特惠_0021Field;
	}

	private static object CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡原价_0021Getter(object P_0)
	{
		return ((购买卡密VM)P_0).月卡原价;
	}

	private static void CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡原价_0021Setter(object P_0, object P_1)
	{
		((购买卡密VM)P_0).月卡原价 = (string)P_1;
	}

	public static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡原价_0021Property()
	{
		if (CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡原价_0021Field != null)
		{
			return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡原价_0021Field;
		}
		CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡原价_0021Field = new ClrPropertyInfo("月卡原价", (object P_0) => ((购买卡密VM)P_0).月卡原价, delegate(object P_0, object P_1)
		{
			((购买卡密VM)P_0).月卡原价 = (string)P_1;
		}, typeof(string));
		return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡原价_0021Field;
	}

	private static object CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡现价_0021Getter(object P_0)
	{
		return ((购买卡密VM)P_0).月卡现价;
	}

	private static void CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡现价_0021Setter(object P_0, object P_1)
	{
		((购买卡密VM)P_0).月卡现价 = (string)P_1;
	}

	public static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡现价_0021Property()
	{
		if (CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡现价_0021Field != null)
		{
			return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡现价_0021Field;
		}
		CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡现价_0021Field = new ClrPropertyInfo("月卡现价", (object P_0) => ((购买卡密VM)P_0).月卡现价, delegate(object P_0, object P_1)
		{
			((购买卡密VM)P_0).月卡现价 = (string)P_1;
		}, typeof(string));
		return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡现价_0021Field;
	}

	private static object CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E调整周卡数量Command_0021Getter(object P_0)
	{
		return ((购买卡密VM)P_0).调整周卡数量Command;
	}

	public static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E调整周卡数量Command_0021Property()
	{
		if (CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E调整周卡数量Command_0021Field != null)
		{
			return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E调整周卡数量Command_0021Field;
		}
		CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E调整周卡数量Command_0021Field = new ClrPropertyInfo("调整周卡数量Command", (object P_0) => ((购买卡密VM)P_0).调整周卡数量Command, null, typeof(IRelayCommand<string>));
		return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E调整周卡数量Command_0021Field;
	}

	private static object CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡数量_0021Getter(object P_0)
	{
		return ((购买卡密VM)P_0).周卡数量;
	}

	private static void CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡数量_0021Setter(object P_0, object P_1)
	{
		((购买卡密VM)P_0).周卡数量 = (int)P_1;
	}

	public static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡数量_0021Property()
	{
		if (CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡数量_0021Field != null)
		{
			return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡数量_0021Field;
		}
		CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡数量_0021Field = new ClrPropertyInfo("周卡数量", (object P_0) => ((购买卡密VM)P_0).周卡数量, delegate(object P_0, object P_1)
		{
			((购买卡密VM)P_0).周卡数量 = (int)P_1;
		}, typeof(int));
		return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E周卡数量_0021Field;
	}

	private static object CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E调整月卡数量Command_0021Getter(object P_0)
	{
		return ((购买卡密VM)P_0).调整月卡数量Command;
	}

	public static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E调整月卡数量Command_0021Property()
	{
		if (CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E调整月卡数量Command_0021Field != null)
		{
			return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E调整月卡数量Command_0021Field;
		}
		CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E调整月卡数量Command_0021Field = new ClrPropertyInfo("调整月卡数量Command", (object P_0) => ((购买卡密VM)P_0).调整月卡数量Command, null, typeof(IRelayCommand<string>));
		return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E调整月卡数量Command_0021Field;
	}

	private static object CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡数量_0021Getter(object P_0)
	{
		return ((购买卡密VM)P_0).月卡数量;
	}

	private static void CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡数量_0021Setter(object P_0, object P_1)
	{
		((购买卡密VM)P_0).月卡数量 = (int)P_1;
	}

	public static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡数量_0021Property()
	{
		if (CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡数量_0021Field != null)
		{
			return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡数量_0021Field;
		}
		CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡数量_0021Field = new ClrPropertyInfo("月卡数量", (object P_0) => ((购买卡密VM)P_0).月卡数量, delegate(object P_0, object P_1)
		{
			((购买卡密VM)P_0).月卡数量 = (int)P_1;
		}, typeof(int));
		return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E月卡数量_0021Field;
	}

	private static object CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E恢复购买记录Command_0021Getter(object P_0)
	{
		return ((购买卡密VM)P_0).恢复购买记录Command;
	}

	public static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E恢复购买记录Command_0021Property()
	{
		if (CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E恢复购买记录Command_0021Field != null)
		{
			return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E恢复购买记录Command_0021Field;
		}
		CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E恢复购买记录Command_0021Field = new ClrPropertyInfo("恢复购买记录Command", (object P_0) => ((购买卡密VM)P_0).恢复购买记录Command, null, typeof(IAsyncRelayCommand));
		return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E恢复购买记录Command_0021Field;
	}

	private static object CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E显示恢复购买按钮_0021Getter(object P_0)
	{
		return ((购买卡密VM)P_0).显示恢复购买按钮;
	}

	private static void CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E显示恢复购买按钮_0021Setter(object P_0, object P_1)
	{
		((购买卡密VM)P_0).显示恢复购买按钮 = (bool)P_1;
	}

	public static IPropertyInfo CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E显示恢复购买按钮_0021Property()
	{
		if (CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E显示恢复购买按钮_0021Field != null)
		{
			return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E显示恢复购买按钮_0021Field;
		}
		CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E显示恢复购买按钮_0021Field = new ClrPropertyInfo("显示恢复购买按钮", (object P_0) => ((购买卡密VM)P_0).显示恢复购买按钮, delegate(object P_0, object P_1)
		{
			((购买卡密VM)P_0).显示恢复购买按钮 = (bool)P_1;
		}, typeof(bool));
		return CursorPro_002EViewModels_002E购买卡密VM_002CCursorPro_002E显示恢复购买按钮_0021Field;
	}

	private static object CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E窗口标题_0021Getter(object P_0)
	{
		return ((主要窗口)P_0).窗口标题;
	}

	private static void CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E窗口标题_0021Setter(object P_0, object P_1)
	{
		((主要窗口)P_0).窗口标题 = (string)P_1;
	}

	public static IPropertyInfo CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E窗口标题_0021Property()
	{
		if (CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E窗口标题_0021Field != null)
		{
			return CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E窗口标题_0021Field;
		}
		CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E窗口标题_0021Field = new ClrPropertyInfo("窗口标题", (object P_0) => ((主要窗口)P_0).窗口标题, delegate(object P_0, object P_1)
		{
			((主要窗口)P_0).窗口标题 = (string)P_1;
		}, typeof(string));
		return CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E窗口标题_0021Field;
	}

	private static object Avalonia_002EStyling_002ESetter_002CAvalonia_002EBase_002EValue_0021Getter(object P_0)
	{
		return ((Setter)P_0).Value;
	}

	private static void Avalonia_002EStyling_002ESetter_002CAvalonia_002EBase_002EValue_0021Setter(object P_0, object P_1)
	{
		((Setter)P_0).Value = P_1;
	}

	public static IPropertyInfo Avalonia_002EStyling_002ESetter_002CAvalonia_002EBase_002EValue_0021Property()
	{
		if (Avalonia_002EStyling_002ESetter_002CAvalonia_002EBase_002EValue_0021Field != null)
		{
			return Avalonia_002EStyling_002ESetter_002CAvalonia_002EBase_002EValue_0021Field;
		}
		Avalonia_002EStyling_002ESetter_002CAvalonia_002EBase_002EValue_0021Field = new ClrPropertyInfo("Value", (object P_0) => ((Setter)P_0).Value, delegate(object P_0, object P_1)
		{
			((Setter)P_0).Value = P_1;
		}, typeof(object));
		return Avalonia_002EStyling_002ESetter_002CAvalonia_002EBase_002EValue_0021Field;
	}

	private static object CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E获取状态文本_0021Getter(object P_0)
	{
		return ((主要窗口)P_0).获取状态文本;
	}

	private static void CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E获取状态文本_0021Setter(object P_0, object P_1)
	{
		((主要窗口)P_0).获取状态文本 = (string)P_1;
	}

	public static IPropertyInfo CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E获取状态文本_0021Property()
	{
		if (CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E获取状态文本_0021Field != null)
		{
			return CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E获取状态文本_0021Field;
		}
		CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E获取状态文本_0021Field = new ClrPropertyInfo("获取状态文本", (object P_0) => ((主要窗口)P_0).获取状态文本, delegate(object P_0, object P_1)
		{
			((主要窗口)P_0).获取状态文本 = (string)P_1;
		}, typeof(string));
		return CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E获取状态文本_0021Field;
	}

	private static object CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E状态文本点击Command_0021Getter(object P_0)
	{
		return ((主要窗口)P_0).状态文本点击Command;
	}

	public static IPropertyInfo CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E状态文本点击Command_0021Property()
	{
		if (CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E状态文本点击Command_0021Field != null)
		{
			return CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E状态文本点击Command_0021Field;
		}
		CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E状态文本点击Command_0021Field = new ClrPropertyInfo("状态文本点击Command", (object P_0) => ((主要窗口)P_0).状态文本点击Command, null, typeof(IRelayCommand));
		return CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E状态文本点击Command_0021Field;
	}

	private static object CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E使用类型文本_0021Getter(object P_0)
	{
		return ((主要窗口)P_0).使用类型文本;
	}

	private static void CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E使用类型文本_0021Setter(object P_0, object P_1)
	{
		((主要窗口)P_0).使用类型文本 = (string)P_1;
	}

	public static IPropertyInfo CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E使用类型文本_0021Property()
	{
		if (CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E使用类型文本_0021Field != null)
		{
			return CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E使用类型文本_0021Field;
		}
		CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E使用类型文本_0021Field = new ClrPropertyInfo("使用类型文本", (object P_0) => ((主要窗口)P_0).使用类型文本, delegate(object P_0, object P_1)
		{
			((主要窗口)P_0).使用类型文本 = (string)P_1;
		}, typeof(string));
		return CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E使用类型文本_0021Field;
	}

	private static object CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E一键获取快速额度Command_0021Getter(object P_0)
	{
		return ((主要窗口)P_0).一键获取快速额度Command;
	}

	public static IPropertyInfo CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E一键获取快速额度Command_0021Property()
	{
		if (CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E一键获取快速额度Command_0021Field != null)
		{
			return CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E一键获取快速额度Command_0021Field;
		}
		CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E一键获取快速额度Command_0021Field = new ClrPropertyInfo("一键获取快速额度Command", (object P_0) => ((主要窗口)P_0).一键获取快速额度Command, null, typeof(IAsyncRelayCommand));
		return CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E一键获取快速额度Command_0021Field;
	}

	private static object CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E使用教程Command_0021Getter(object P_0)
	{
		return ((主要窗口)P_0).使用教程Command;
	}

	public static IPropertyInfo CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E使用教程Command_0021Property()
	{
		if (CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E使用教程Command_0021Field != null)
		{
			return CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E使用教程Command_0021Field;
		}
		CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E使用教程Command_0021Field = new ClrPropertyInfo("使用教程Command", (object P_0) => ((主要窗口)P_0).使用教程Command, null, typeof(IRelayCommand));
		return CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E使用教程Command_0021Field;
	}

	private static object CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E显示加速Cursor功能_0021Getter(object P_0)
	{
		return ((主要窗口)P_0).显示加速Cursor功能;
	}

	private static void CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E显示加速Cursor功能_0021Setter(object P_0, object P_1)
	{
		((主要窗口)P_0).显示加速Cursor功能 = (bool)P_1;
	}

	public static IPropertyInfo CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E显示加速Cursor功能_0021Property()
	{
		if (CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E显示加速Cursor功能_0021Field != null)
		{
			return CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E显示加速Cursor功能_0021Field;
		}
		CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E显示加速Cursor功能_0021Field = new ClrPropertyInfo("显示加速Cursor功能", (object P_0) => ((主要窗口)P_0).显示加速Cursor功能, delegate(object P_0, object P_1)
		{
			((主要窗口)P_0).显示加速Cursor功能 = (bool)P_1;
		}, typeof(bool));
		return CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E显示加速Cursor功能_0021Field;
	}

	private static object CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E加速Cursor已启用_0021Getter(object P_0)
	{
		return ((主要窗口)P_0).加速Cursor已启用;
	}

	private static void CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E加速Cursor已启用_0021Setter(object P_0, object P_1)
	{
		((主要窗口)P_0).加速Cursor已启用 = (bool)P_1;
	}

	public static IPropertyInfo CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E加速Cursor已启用_0021Property()
	{
		if (CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E加速Cursor已启用_0021Field != null)
		{
			return CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E加速Cursor已启用_0021Field;
		}
		CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E加速Cursor已启用_0021Field = new ClrPropertyInfo("加速Cursor已启用", (object P_0) => ((主要窗口)P_0).加速Cursor已启用, delegate(object P_0, object P_1)
		{
			((主要窗口)P_0).加速Cursor已启用 = (bool)P_1;
		}, typeof(bool));
		return CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E加速Cursor已启用_0021Field;
	}

	private static object CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E加速Cursor切换Command_0021Getter(object P_0)
	{
		return ((主要窗口)P_0).加速Cursor切换Command;
	}

	public static IPropertyInfo CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E加速Cursor切换Command_0021Property()
	{
		if (CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E加速Cursor切换Command_0021Field != null)
		{
			return CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E加速Cursor切换Command_0021Field;
		}
		CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E加速Cursor切换Command_0021Field = new ClrPropertyInfo("加速Cursor切换Command", (object P_0) => ((主要窗口)P_0).加速Cursor切换Command, null, typeof(IAsyncRelayCommand));
		return CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E加速Cursor切换Command_0021Field;
	}

	private static object CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E版本链接Command_0021Getter(object P_0)
	{
		return ((主要窗口)P_0).版本链接Command;
	}

	public static IPropertyInfo CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E版本链接Command_0021Property()
	{
		if (CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E版本链接Command_0021Field != null)
		{
			return CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E版本链接Command_0021Field;
		}
		CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E版本链接Command_0021Field = new ClrPropertyInfo("版本链接Command", (object P_0) => ((主要窗口)P_0).版本链接Command, null, typeof(IRelayCommand));
		return CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E版本链接Command_0021Field;
	}

	private static object CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E版本链接边距_0021Getter(object P_0)
	{
		return ((主要窗口)P_0).版本链接边距;
	}

	public static IPropertyInfo CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E版本链接边距_0021Property()
	{
		if (CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E版本链接边距_0021Field != null)
		{
			return CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E版本链接边距_0021Field;
		}
		CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E版本链接边距_0021Field = new ClrPropertyInfo("版本链接边距", (object P_0) => ((主要窗口)P_0).版本链接边距, null, typeof(Thickness));
		return CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E版本链接边距_0021Field;
	}

	private static object CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E版本号_0021Getter(object P_0)
	{
		return ((主要窗口)P_0).版本号;
	}

	private static void CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E版本号_0021Setter(object P_0, object P_1)
	{
		((主要窗口)P_0).版本号 = (string)P_1;
	}

	public static IPropertyInfo CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E版本号_0021Property()
	{
		if (CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E版本号_0021Field != null)
		{
			return CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E版本号_0021Field;
		}
		CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E版本号_0021Field = new ClrPropertyInfo("版本号", (object P_0) => ((主要窗口)P_0).版本号, delegate(object P_0, object P_1)
		{
			((主要窗口)P_0).版本号 = (string)P_1;
		}, typeof(string));
		return CursorPro_002EViewModels_002E主要窗口_002CCursorPro_002E版本号_0021Field;
	}
}
