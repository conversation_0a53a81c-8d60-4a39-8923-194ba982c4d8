using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.Versioning;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using CursorPro.Models;
using Microsoft.Data.Sqlite;
using MsBox.Avalonia.Enums;

namespace CursorPro.ViewModels;

public class 重置机器码
{
	private readonly PlatformPaths _paths;

	public 重置机器码()
	{
		_paths = new PlatformPaths();
	}

	public async Task<bool> 执行重置机器码()
	{
		return await 执行重置机器码(null, null, null);
	}

	public async Task<bool> 执行重置机器码(string? 邮箱, string? 访问令牌, string? 刷新令牌, string? proxyServer = null)
	{
		_ = 4;
		try
		{
			if (!CheckInstallation())
			{
				return false;
			}
			string cursorPath = GetCursorPath();
			if (string.IsNullOrEmpty(cursorPath))
			{
				await Tools.显示消息框Async("错误", "首次使用请先启动Cursor再获取", ButtonEnum.Ok, Icon.Error);
				return false;
			}
			if (!(await CursorProcessManager.CloseCursorAsync()))
			{
				await Tools.显示消息框Async("错误", "无法关闭Cursor进程，请手动关闭后再试", ButtonEnum.Ok, Icon.Error);
				return false;
			}
			if (!ExecuteReset(邮箱, 访问令牌, 刷新令牌, proxyServer))
			{
				await Tools.显示消息框Async("错误", "重置操作失败", ButtonEnum.Ok, Icon.Error);
				return false;
			}
			处理代理服务器设置(proxyServer);
			await CursorProcessManager.StartCursorAsync(cursorPath);
			return true;
		}
		catch (Exception)
		{
			return false;
		}
	}

	private bool CheckInstallation()
	{
		if (OperatingSystem.IsWindows())
		{
			return CheckWindowsInstallation();
		}
		if (OperatingSystem.IsMacOS())
		{
			return CheckMacInstallation();
		}
		return false;
	}

	private string? GetCursorPath()
	{
		return CursorProcessManager.GetCursorPath();
	}

	private bool ExecuteReset(string? 邮箱, string? 访问令牌, string? 刷新令牌, string? proxyServer = null)
	{
		try
		{
			if (!GenerateNewConfig(邮箱, 访问令牌, 刷新令牌))
			{
				return false;
			}
			new 禁用更新(_paths).执行禁用更新();
			if (CheckCursorVersion())
			{
				PatchCursorGetMachineId();
			}
			LogCompletionDetails();
			return true;
		}
		catch (Exception)
		{
			return false;
		}
	}

	public bool ModifySettingsJson(string proxyServer)
	{
		try
		{
			string settingsJsonPath = GetSettingsJsonPath();
			string directoryName = Path.GetDirectoryName(settingsJsonPath);
			if (!string.IsNullOrEmpty(directoryName))
			{
				Directory.CreateDirectory(directoryName);
			}
			JsonObject jsonObject;
			if (File.Exists(settingsJsonPath))
			{
				string json = File.ReadAllText(settingsJsonPath);
				try
				{
					jsonObject = JsonSerializer.Deserialize<JsonObject>(json) ?? new JsonObject();
				}
				catch (JsonException)
				{
					jsonObject = new JsonObject();
				}
			}
			else
			{
				jsonObject = new JsonObject();
			}
			if (string.IsNullOrEmpty(proxyServer))
			{
				jsonObject.Remove("http.proxy");
				jsonObject.Remove("cursor.general.disableHttp2");
				jsonObject.Remove("http.systemCertificates");
				jsonObject.Remove("http.proxySupport");
				jsonObject.Remove("http.experimental.systemCertificatesV2");
				jsonObject.Remove("http.experimental.systemCertificates");
				string contents = JsonSerializer.Serialize(jsonObject, new JsonSerializerOptions
				{
					WriteIndented = true
				});
				File.WriteAllText(settingsJsonPath, contents);
				return false;
			}
			jsonObject["http.proxy"] = proxyServer;
			jsonObject["cursor.general.disableHttp2"] = true;
			jsonObject["http.systemCertificates"] = false;
			jsonObject["http.proxySupport"] = "override";
			jsonObject["http.experimental.systemCertificatesV2"] = false;
			jsonObject["http.experimental.systemCertificates"] = false;
			string contents2 = JsonSerializer.Serialize(jsonObject, new JsonSerializerOptions
			{
				WriteIndented = true
			});
			File.WriteAllText(settingsJsonPath, contents2);
			return true;
		}
		catch (Exception)
		{
			return false;
		}
	}

	private string GetSettingsJsonPath()
	{
		if (OperatingSystem.IsWindows())
		{
			return Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Cursor", "User", "settings.json");
		}
		if (OperatingSystem.IsMacOS())
		{
			string folderPath = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
			return Path.Combine(folderPath, "Library", "Application Support", "Cursor", "User", "settings.json");
		}
		throw new PlatformNotSupportedException("不支持的操作系统");
	}

	public bool DeleteProxySettings()
	{
		try
		{
			string settingsJsonPath = GetSettingsJsonPath();
			if (!File.Exists(settingsJsonPath))
			{
				return true;
			}
			string json = File.ReadAllText(settingsJsonPath);
			JsonObject jsonObject;
			try
			{
				jsonObject = JsonSerializer.Deserialize<JsonObject>(json) ?? new JsonObject();
			}
			catch (JsonException)
			{
				return false;
			}
			string[] obj = new string[6] { "http.proxy", "cursor.general.disableHttp2", "http.systemCertificates", "http.proxySupport", "http.experimental.systemCertificatesV2", "http.experimental.systemCertificates" };
			int num = 0;
			string[] array = obj;
			foreach (string propertyName in array)
			{
				if (jsonObject.Remove(propertyName))
				{
					num++;
				}
			}
			string contents = JsonSerializer.Serialize(jsonObject, new JsonSerializerOptions
			{
				WriteIndented = true
			});
			File.WriteAllText(settingsJsonPath, contents);
			return true;
		}
		catch (Exception)
		{
			return false;
		}
	}

	private void LogCompletionDetails()
	{
	}

	[SupportedOSPlatform("windows")]
	private bool CheckWindowsInstallation()
	{
		string directoryName = Path.GetDirectoryName(_paths.DbPath);
		if (string.IsNullOrEmpty(directoryName) || !Directory.Exists(directoryName))
		{
			return false;
		}
		return true;
	}

	[SupportedOSPlatform("macos")]
	private bool CheckMacInstallation()
	{
		if (!Directory.Exists("/Applications/Cursor.app"))
		{
			return false;
		}
		string[] array = new string[3] { _paths.PackageJsonPath, _paths.MainJsPath, _paths.ProductJsonPath };
		for (int i = 0; i < array.Length; i++)
		{
			File.Exists(array[i]);
		}
		string directoryName = Path.GetDirectoryName(_paths.DbPath);
		if (string.IsNullOrEmpty(directoryName) || !Directory.Exists(directoryName))
		{
			return false;
		}
		return true;
	}

	private bool GenerateNewConfig(string? 邮箱, string? 访问令牌, string? 刷新令牌)
	{
		try
		{
			if (!File.Exists(_paths.DbPath))
			{
				return false;
			}
			if (!EnsureKeyFilesWritable())
			{
				return false;
			}
			Dictionary<string, string> dictionary = GenerateNewIds();
			foreach (KeyValuePair<string, string> item in dictionary)
			{
				_ = item;
			}
			if (!UpdateStorageJson(dictionary))
			{
				return false;
			}
			UpdateMachineIdFile(dictionary["storage.serviceMachineId"]);
			UpdateDatabaseInTransaction(dictionary["storage.serviceMachineId"], 邮箱, 访问令牌, 刷新令牌);
			return true;
		}
		catch (Exception)
		{
			return false;
		}
	}

	private Dictionary<string, string> GenerateNewIds()
	{
		Dictionary<string, string> dictionary = new Dictionary<string, string>();
		string value = Guid.NewGuid().ToString("N");
		string value2 = Guid.NewGuid().ToString("N");
		string value3 = Guid.NewGuid().ToString("N");
		string value4 = Guid.NewGuid().ToString("N");
		string value5 = Guid.NewGuid().ToString();
		dictionary["telemetry.machineId"] = value;
		dictionary["telemetry.macMachineId"] = value2;
		dictionary["telemetry.devDeviceId"] = value3;
		dictionary["telemetry.sqmId"] = value4;
		dictionary["storage.serviceMachineId"] = value5;
		return dictionary;
	}

	private bool UpdateStorageJson(Dictionary<string, string> newIds)
	{
		try
		{
			string json = File.ReadAllText(_paths.DbPath);
			_ = JsonDocument.Parse(json).RootElement;
			Dictionary<string, object> dictionary = new Dictionary<string, object>();
			using (JsonDocument jsonDocument = JsonDocument.Parse(json))
			{
				foreach (JsonProperty item in jsonDocument.RootElement.EnumerateObject())
				{
					dictionary[item.Name] = item.Value.Clone();
				}
			}
			foreach (KeyValuePair<string, string> newId in newIds)
			{
				dictionary[newId.Key] = newId.Value;
			}
			string contents = JsonSerializer.Serialize(dictionary, new JsonSerializerOptions
			{
				WriteIndented = true
			});
			File.WriteAllText(_paths.DbPath, contents);
			return true;
		}
		catch (Exception)
		{
			return false;
		}
	}

	private bool UpdateMachineIdFile(string uuidMachineId)
	{
		try
		{
			string directoryName = Path.GetDirectoryName(_paths.MachineIdPath);
			if (!string.IsNullOrEmpty(directoryName))
			{
				Directory.CreateDirectory(directoryName);
			}
			File.WriteAllText(_paths.MachineIdPath, uuidMachineId);
			return true;
		}
		catch (Exception)
		{
			return false;
		}
	}

	private bool UpdateDatabaseInTransaction(string uuidMachineId, string? 邮箱 = null, string? 访问令牌 = null, string? 刷新令牌 = null)
	{
		try
		{
			if (!File.Exists(_paths.SqlitePath))
			{
				return false;
			}
			string[] array = new string[5] { "__$__targetStorageMarker", "cursorai/serverConfig", "telemetry.currentSessionDate", "aiCodeTrackingLines", "telemetry.lastSessionDate" };
			using SqliteConnection sqliteConnection = new SqliteConnection("Data Source=" + _paths.SqlitePath);
			sqliteConnection.Open();
			using SqliteTransaction sqliteTransaction = sqliteConnection.BeginTransaction();
			try
			{
				int num = 0;
				int num2 = 0;
				string[] array2 = array;
				foreach (string value in array2)
				{
					using SqliteCommand sqliteCommand = new SqliteCommand("DELETE FROM ItemTable WHERE key = @key", sqliteConnection);
					sqliteCommand.Transaction = sqliteTransaction;
					sqliteCommand.Parameters.AddWithValue("@key", value);
					int num3 = sqliteCommand.ExecuteNonQuery();
					num += num3;
					_ = 0;
				}
				using (SqliteCommand sqliteCommand2 = new SqliteCommand("INSERT OR REPLACE INTO ItemTable (key, value) VALUES (@key, @value)", sqliteConnection))
				{
					sqliteCommand2.Transaction = sqliteTransaction;
					sqliteCommand2.Parameters.AddWithValue("@key", "storage.serviceMachineId");
					sqliteCommand2.Parameters.AddWithValue("@value", uuidMachineId);
					int num4 = sqliteCommand2.ExecuteNonQuery();
					num2 += num4;
				}
				if (!string.IsNullOrEmpty(邮箱) && !string.IsNullOrEmpty(访问令牌) && !string.IsNullOrEmpty(刷新令牌))
				{
					using (SqliteCommand sqliteCommand3 = new SqliteCommand("INSERT OR REPLACE INTO ItemTable (key, value) VALUES (@key, @value)", sqliteConnection))
					{
						sqliteCommand3.Transaction = sqliteTransaction;
						sqliteCommand3.Parameters.AddWithValue("@key", "cursorAuth/cachedEmail");
						sqliteCommand3.Parameters.AddWithValue("@value", 邮箱);
						sqliteCommand3.ExecuteNonQuery();
						num2++;
					}
					using (SqliteCommand sqliteCommand4 = new SqliteCommand("INSERT OR REPLACE INTO ItemTable (key, value) VALUES (@key, @value)", sqliteConnection))
					{
						sqliteCommand4.Transaction = sqliteTransaction;
						sqliteCommand4.Parameters.AddWithValue("@key", "cursorAuth/accessToken");
						sqliteCommand4.Parameters.AddWithValue("@value", 访问令牌);
						sqliteCommand4.ExecuteNonQuery();
						num2++;
					}
					using SqliteCommand sqliteCommand5 = new SqliteCommand("INSERT OR REPLACE INTO ItemTable (key, value) VALUES (@key, @value)", sqliteConnection);
					sqliteCommand5.Transaction = sqliteTransaction;
					sqliteCommand5.Parameters.AddWithValue("@key", "cursorAuth/refreshToken");
					sqliteCommand5.Parameters.AddWithValue("@value", 刷新令牌);
					sqliteCommand5.ExecuteNonQuery();
					num2++;
				}
				using (SqliteCommand sqliteCommand6 = new SqliteCommand("INSERT OR REPLACE INTO ItemTable (key, value) VALUES (@key, @value)", sqliteConnection))
				{
					sqliteCommand6.Transaction = sqliteTransaction;
					sqliteCommand6.Parameters.AddWithValue("@key", "cursorAuth/cachedSignUpType");
					sqliteCommand6.Parameters.AddWithValue("@value", "github");
					sqliteCommand6.ExecuteNonQuery();
					num2++;
				}
				sqliteTransaction.Commit();
				return true;
			}
			catch (Exception)
			{
				sqliteTransaction.Rollback();
				return false;
			}
		}
		catch (Exception)
		{
			return false;
		}
		finally
		{
			SqliteConnection.ClearAllPools();
		}
	}

	private bool EnsureKeyFilesWritable()
	{
		var obj = new[]
		{
			new
			{
				Path = _paths.DbPath,
				Name = "storage.json",
				Optional = false
			},
			new
			{
				Path = _paths.SqlitePath,
				Name = "state.vscdb",
				Optional = false
			},
			new
			{
				Path = _paths.MachineIdPath,
				Name = "machineId",
				Optional = true
			},
			new
			{
				Path = _paths.MainJsPath,
				Name = "main.js",
				Optional = true
			}
		};
		bool result = true;
		int num = 0;
		int num2 = 0;
		int num3 = 0;
		var array = obj;
		foreach (var anon in array)
		{
			if (anon.Optional && !File.Exists(anon.Path))
			{
				num2++;
			}
			else if (!FileHelper.EnsureFileWritable(anon.Path))
			{
				if (anon.Optional)
				{
					num3++;
				}
				else
				{
					result = false;
				}
			}
			else
			{
				num++;
			}
		}
		return result;
	}

	private bool CheckCursorVersion()
	{
		try
		{
			if (!File.Exists(_paths.PackageJsonPath))
			{
				return false;
			}
			using JsonDocument jsonDocument = JsonDocument.Parse(File.ReadAllText(_paths.PackageJsonPath));
			if (jsonDocument.RootElement.TryGetProperty("version", out var value))
			{
				string text = value.GetString();
				if (string.IsNullOrEmpty(text))
				{
					return false;
				}
				if (!Regex.IsMatch(text, "^\\d+\\.\\d+\\.\\d+$"))
				{
					return false;
				}
				int[] array = text.Split('.').Select(int.Parse).ToArray();
				int[] array2 = new int[3] { 0, 45, 0 };
				for (int i = 0; i < 3; i++)
				{
					if (array[i] > array2[i])
					{
						return true;
					}
					if (array[i] < array2[i])
					{
						return false;
					}
				}
				return true;
			}
			return false;
		}
		catch (Exception)
		{
			return false;
		}
	}

	private bool PatchCursorGetMachineId()
	{
		try
		{
			if (!File.Exists(_paths.MainJsPath))
			{
				return false;
			}
			if (!ModifyMainJs())
			{
				return false;
			}
			return true;
		}
		catch (Exception)
		{
			return false;
		}
	}

	private bool ModifyMainJs()
	{
		try
		{
			string text = File.ReadAllText(_paths.MainJsPath);
			bool flag = false;
			string pattern = "async getMachineId\\(\\)\\{return [^??]+\\?\\?([^}]+)\\}";
			string replacement = "async getMachineId(){return $1}";
			string pattern2 = "async getMacMachineId\\(\\)\\{return [^??]+\\?\\?([^}]+)\\}";
			string replacement2 = "async getMacMachineId(){return $1}";
			string text2 = Regex.Replace(text, pattern, replacement);
			if (text2 != text)
			{
				text = text2;
				flag = true;
			}
			text2 = Regex.Replace(text, pattern2, replacement2);
			if (text2 != text)
			{
				text = text2;
				flag = true;
			}
			if (flag)
			{
				File.WriteAllText(_paths.MainJsPath, text);
				return true;
			}
			return true;
		}
		catch (Exception)
		{
			return false;
		}
	}

	private void 处理代理服务器设置(string? 传入的代理服务器)
	{
		try
		{
			if (!string.IsNullOrEmpty(传入的代理服务器))
			{
				ModifySettingsJson(传入的代理服务器);
			}
			else
			{
				DeleteProxySettings();
			}
		}
		catch (Exception)
		{
			DeleteProxySettings();
		}
	}
}
