using System.Runtime.InteropServices;

namespace CursorPro;

public static class Info
{
	public static string AppName = "CursorPro";

	public static string 本地版本号 = "2.2.3";

	public static string 机器码 = "";

	public static string API基础地址 = "http://119.45.250.178";

	public static string 免费服务端口 = "5269";

	public static string 付费服务端口 = "5270";

	public static string 支付服务端口 = "5001";

	public static string 到期时间 = "";

	public static string 平台类型 = (RuntimeInformation.IsOSPlatform(OSPlatform.Windows) ? "Windows" : "Mac");

	public static int 剩余额度 = 0;

	public static string 代理 = "5urbID30A8w7";

	public static string Windows下载链接 = "https://wwcc.lanzoue.com/b00od5g23a";

	public static string Mac下载链接 = "https://wwcc.lanzoue.com/b00od5g23a";

	public static string 默认下载链接 = "https://wwcc.lanzoue.com/b00od5g23a";

	public static string 使用教程链接 = "https://hiioj3vbs6o.feishu.cn/wiki/JGtHwp6fyif07vkHjQNcQEV6nwN?from=from_copylink";

	public static string API地址 => API基础地址 + ":" + 免费服务端口;

	public static string 获取下载链接()
	{
		string text = 平台类型;
		if (!(text == "Windows"))
		{
			if (text == "Mac")
			{
				return Mac下载链接;
			}
			return 默认下载链接;
		}
		return Windows下载链接;
	}
}
