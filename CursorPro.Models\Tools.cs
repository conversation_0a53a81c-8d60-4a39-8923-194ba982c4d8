using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using Avalonia.Threading;
using MsBox.Avalonia;
using MsBox.Avalonia.Enums;

namespace CursorPro.Models;

public class Tools
{
	public static bool OpenUrl(string url)
	{
		try
		{
			if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
			{
				Process.Start(new ProcessStartInfo
				{
					FileName = url,
					UseShellExecute = true
				});
			}
			else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
			{
				Process.Start("open", url);
			}
			else
			{
				Process.Start(new ProcessStartInfo
				{
					FileName = url,
					UseShellExecute = true
				});
			}
			return true;
		}
		catch (Exception)
		{
			return false;
		}
	}

	public static void 显示消息框(string 标题, string 内容, ButtonEnum 按钮类型 = ButtonEnum.Ok, Icon 图标类型 = Icon.Info)
	{
		if (Dispatcher.UIThread.CheckAccess())
		{
			MessageBoxManager.GetMessageBoxStandard(标题, 内容, 按钮类型, 图标类型).ShowAsync().Wait();
			return;
		}
		Dispatcher.UIThread.InvokeAsync(() => MessageBoxManager.GetMessageBoxStandard(标题, 内容, 按钮类型, 图标类型).ShowAsync()).Wait();
	}

	public static async Task 显示消息框Async(string 标题, string 内容, ButtonEnum 按钮类型 = ButtonEnum.Ok, Icon 图标类型 = Icon.Info)
	{
		if (Dispatcher.UIThread.CheckAccess())
		{
			await MessageBoxManager.GetMessageBoxStandard(标题, 内容, 按钮类型, 图标类型).ShowAsync();
			return;
		}
		await Dispatcher.UIThread.InvokeAsync(async delegate
		{
			await MessageBoxManager.GetMessageBoxStandard(标题, 内容, 按钮类型, 图标类型).ShowAsync();
		});
	}

	public static async Task<ButtonResult> 显示确认消息框Async(string 标题, string 内容, ButtonEnum 按钮类型 = ButtonEnum.Ok, Icon 图标类型 = Icon.Info)
	{
		if (Dispatcher.UIThread.CheckAccess())
		{
			return await MessageBoxManager.GetMessageBoxStandard(标题, 内容, 按钮类型, 图标类型).ShowAsync();
		}
		return await Dispatcher.UIThread.InvokeAsync(async () => await MessageBoxManager.GetMessageBoxStandard(标题, 内容, 按钮类型, 图标类型).ShowAsync());
	}

	public static async Task<string?> 显示输入框Async(string 标题, string 提示文字, string 默认值 = "")
	{
		try
		{
			return await new 输入对话框(标题, 提示文字, 默认值).GetResultAsync();
		}
		catch (Exception)
		{
			return null;
		}
	}
}
