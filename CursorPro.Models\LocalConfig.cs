using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Text.Json.Nodes;

namespace CursorPro.Models;

public static class LocalConfig
{
	private static readonly string 配置目录;

	public static readonly string 默认配置文件;

	private static JsonNode? _jsonObject;

	private static string GetConfigDirectory()
	{
		if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
		{
			return Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "NVIDIA_NV");
		}
		if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
		{
			return Path.Combine(Environment.GetEnvironmentVariable("HOME") ?? ("/Users/" + Environment.UserName), "Library", "Application Support", "NVIDIA_NV");
		}
		return Path.Combine(Environment.GetEnvironmentVariable("HOME") ?? Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), ".NVIDIA_NV");
	}

	static LocalConfig()
	{
		配置目录 = GetConfigDirectory();
		默认配置文件 = Path.Combine(配置目录, "system_cache.dat");
		初始化();
	}

	private static void 初始化()
	{
		try
		{
			if (!Directory.Exists(配置目录))
			{
				Directory.CreateDirectory(配置目录);
			}
			if (File.Exists(默认配置文件))
			{
				string text = File.ReadAllText(默认配置文件);
				if (string.IsNullOrWhiteSpace(text))
				{
					text = "{}";
					File.WriteAllText(默认配置文件, text);
				}
				_jsonObject = JsonNode.Parse(text);
			}
			else
			{
				_jsonObject = new JsonObject();
				File.WriteAllText(默认配置文件, "{}");
			}
		}
		catch (Exception)
		{
			_jsonObject = new JsonObject();
		}
	}

	public static string? 读取(string 键名)
	{
		try
		{
			JsonNode jsonNode = _jsonObject?[键名];
			string result = null;
			if (jsonNode != null)
			{
				result = ((!(jsonNode is JsonValue jsonValue)) ? jsonNode.ToString() : jsonValue.GetValue<object>()?.ToString());
			}
			return result;
		}
		catch (Exception)
		{
			return null;
		}
	}

	public static void 写入(string 键名, object? 值)
	{
		try
		{
			if (_jsonObject == null)
			{
				_jsonObject = new JsonObject();
			}
			_jsonObject[键名] = JsonValue.Create(值);
			File.WriteAllText(默认配置文件, _jsonObject.ToJsonString());
		}
		catch (Exception)
		{
		}
	}
}
