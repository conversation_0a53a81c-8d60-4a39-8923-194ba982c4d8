using System.Threading.Tasks;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Layout;

namespace CursorPro.Models;

internal class 输入对话框 : Window
{
	private TextBox? _textBox;

	private string? _result;

	private readonly TaskCompletionSource<string?> _taskCompletionSource = new TaskCompletionSource<string>();

	public 输入对话框(string 标题, string 提示文字, string 默认值)
	{
		base.Title = 标题;
		base.Width = 350.0;
		base.Height = 180.0;
		base.WindowStartupLocation = WindowStartupLocation.CenterScreen;
		base.CanResize = false;
		InitializeContent(提示文字, 默认值);
		base.Closed += delegate
		{
			if (!_taskCompletionSource.Task.IsCompleted)
			{
				_taskCompletionSource.SetResult(_result);
			}
		};
	}

	public async Task<string?> GetResultAsync()
	{
		Show();
		return await _taskCompletionSource.Task;
	}

	private void InitializeContent(string 提示文字, string 默认值)
	{
		Grid grid = new Grid();
		grid.RowDefinitions.Add(new RowDefinition(GridLength.Auto));
		grid.RowDefinitions.Add(new RowDefinition(GridLength.Auto));
		grid.RowDefinitions.Add(new RowDefinition(GridLength.Star));
		grid.RowDefinitions.Add(new RowDefinition(GridLength.Auto));
		TextBlock textBlock = new TextBlock
		{
			Text = 提示文字,
			Margin = new Thickness(20.0, 20.0, 20.0, 10.0),
			FontSize = 14.0
		};
		Grid.SetRow(textBlock, 0);
		grid.Children.Add(textBlock);
		_textBox = new TextBox
		{
			Text = 默认值,
			Margin = new Thickness(20.0, 0.0, 20.0, 10.0),
			FontSize = 14.0
		};
		Grid.SetRow(_textBox, 1);
		grid.Children.Add(_textBox);
		StackPanel stackPanel = new StackPanel
		{
			Orientation = Orientation.Horizontal,
			HorizontalAlignment = HorizontalAlignment.Right,
			Margin = new Thickness(20.0, 10.0, 20.0, 20.0)
		};
		Button button = new Button
		{
			Content = "取消",
			Width = 80.0,
			Height = 32.0,
			Margin = new Thickness(0.0, 0.0, 10.0, 0.0)
		};
		button.Click += delegate
		{
			_result = null;
			_taskCompletionSource.SetResult(null);
			Close();
		};
		Button button2 = new Button
		{
			Content = "确定",
			Width = 80.0,
			Height = 32.0,
			IsDefault = true
		};
		button2.Click += delegate
		{
			_result = _textBox?.Text;
			_taskCompletionSource.SetResult(_result);
			Close();
		};
		stackPanel.Children.Add(button);
		stackPanel.Children.Add(button2);
		Grid.SetRow(stackPanel, 3);
		grid.Children.Add(stackPanel);
		base.Content = grid;
		base.Loaded += delegate
		{
			_textBox?.Focus();
		};
		base.KeyDown += delegate(object? s, KeyEventArgs e)
		{
			if (e.Key == Key.Return)
			{
				_result = _textBox?.Text;
				_taskCompletionSource.SetResult(_result);
				Close();
			}
			else if (e.Key == Key.Escape)
			{
				_result = null;
				_taskCompletionSource.SetResult(null);
				Close();
			}
		};
	}
}
