using System.ComponentModel;

namespace CommunityToolkit.Mvvm.ComponentModel.__Internals
{
    internal static class __KnownINotifyPropertyChangedArgs
    {
        public static readonly PropertyChangedEventArgs IsProcessing = new PropertyChangedEventArgs("IsProcessing");
        public static readonly PropertyChangedEventArgs 显示恢复购买按钮 = new PropertyChangedEventArgs("显示恢复购买按钮");
        public static readonly PropertyChangedEventArgs CurrentOrderNo = new PropertyChangedEventArgs("CurrentOrderNo");
        public static readonly PropertyChangedEventArgs 价格加载中 = new PropertyChangedEventArgs("价格加载中");
        public static readonly PropertyChangedEventArgs 周卡原价 = new PropertyChangedEventArgs("周卡原价");
        public static readonly PropertyChangedEventArgs 周卡现价 = new PropertyChangedEventArgs("周卡现价");
        public static readonly PropertyChangedEventArgs 月卡原价 = new PropertyChangedEventArgs("月卡原价");
        public static readonly PropertyChangedEventArgs 月卡现价 = new PropertyChangedEventArgs("月卡现价");
        public static readonly PropertyChangedEventArgs 周卡数量 = new PropertyChangedEventArgs("周卡数量");
        public static readonly PropertyChangedEventArgs 月卡数量 = new PropertyChangedEventArgs("月卡数量");
        public static readonly PropertyChangedEventArgs 周卡总计信息 = new PropertyChangedEventArgs("周卡总计信息");
        public static readonly PropertyChangedEventArgs 月卡总计信息 = new PropertyChangedEventArgs("月卡总计信息");
    }
}
