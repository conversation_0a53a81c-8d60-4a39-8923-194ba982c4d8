# CursorPro 项目依赖关系分析

## 项目整体架构图

```mermaid
graph TB
    subgraph "CursorPro Application"
        A[Program.cs - 程序入口] --> B[App.axaml - 应用程序]
        B --> C[主要窗口.axaml - 主界面]
        C --> D[购买卡密.axaml - 购买界面]
    end
    
    subgraph "ViewModels Layer"
        E[ViewModelBase - 基础VM]
        F[主要窗口.cs - 主窗口VM]
        G[购买卡密VM.cs - 购买VM]
        H[检查限制.cs - 验证逻辑]
        
        E --> F
        E --> G
        F --> H
    end
    
    subgraph "Models Layer"
        I[CursorProcessManager - 进程管理]
        J[HttpService - 网络服务]
        K[LocalConfig - 本地配置]
        L[Tools - 工具类]
        M[验证结果 - 数据模型]
    end
    
    subgraph "Business Logic"
        N[获取设备标识 - 设备ID]
        O[重置机器码 - 重置逻辑]
        P[界面修改器 - UI修改]
        Q[PlatformPaths - 路径管理]
    end
    
    C --> F
    D --> G
    F --> I
    F --> J
    F --> N
    F --> O
    G --> J
    G --> K
    H --> J
    H --> N
    O --> I
    O --> K
    P --> Q
    P --> I
```

## 核心依赖关系图

```mermaid
graph LR
    subgraph "External Dependencies"
        EXT1[Avalonia UI Framework]
        EXT2[CommunityToolkit.Mvvm]
        EXT3[MessageBox.Avalonia]
        EXT4[System.Text.Json]
        EXT5[System.Net.Http]
    end
    
    subgraph "Core Components"
        CORE1[主要窗口 ViewModel]
        CORE2[检查限制 Service]
        CORE3[HttpService]
        CORE4[CursorProcessManager]
        CORE5[LocalConfig]
    end
    
    subgraph "Platform Specific"
        PLAT1[Windows APIs]
        PLAT2[macOS Shell Commands]
        PLAT3[Network Interfaces]
    end
    
    EXT1 --> CORE1
    EXT2 --> CORE1
    EXT3 --> CORE1
    EXT4 --> CORE3
    EXT5 --> CORE3
    
    CORE1 --> CORE2
    CORE1 --> CORE4
    CORE2 --> CORE3
    CORE2 --> CORE5
    
    CORE4 --> PLAT1
    CORE4 --> PLAT2
    CORE2 --> PLAT3
```

## 数据流依赖图

```mermaid
flowchart TD
    subgraph "Data Sources"
        DS1[本地配置文件]
        DS2[远程API服务器]
        DS3[系统信息]
        DS4[Cursor应用文件]
    end
    
    subgraph "Data Access Layer"
        DAL1[LocalConfig.cs]
        DAL2[HttpService.cs]
        DAL3[获取设备标识.cs]
        DAL4[CursorProcessManager.cs]
    end
    
    subgraph "Business Logic Layer"
        BLL1[检查限制.cs]
        BLL2[重置机器码.cs]
        BLL3[界面修改器.cs]
        BLL4[购买卡密VM.cs]
    end
    
    subgraph "Presentation Layer"
        PL1[主要窗口.cs]
        PL2[购买卡密.axaml]
    end
    
    DS1 --> DAL1
    DS2 --> DAL2
    DS3 --> DAL3
    DS4 --> DAL4
    
    DAL1 --> BLL1
    DAL1 --> BLL2
    DAL2 --> BLL1
    DAL2 --> BLL4
    DAL3 --> BLL1
    DAL3 --> BLL2
    DAL4 --> BLL2
    DAL4 --> BLL3
    
    BLL1 --> PL1
    BLL2 --> PL1
    BLL3 --> PL1
    BLL4 --> PL2
```

## 模块间通信图

```mermaid
sequenceDiagram
    participant UI as 用户界面
    participant VM as ViewModel
    participant BL as 业务逻辑
    participant API as 网络服务
    participant SYS as 系统服务
    
    UI->>VM: 用户操作
    VM->>BL: 调用业务方法
    
    alt 需要网络验证
        BL->>API: HTTP请求
        API-->>BL: 返回结果
    end
    
    alt 需要系统操作
        BL->>SYS: 系统调用
        SYS-->>BL: 操作结果
    end
    
    BL-->>VM: 返回处理结果
    VM-->>UI: 更新界面状态
```

## 文件依赖关系

```mermaid
graph TD
    subgraph "Configuration Files"
        CF1[CursorPro.csproj]
        CF2[app.manifest]
        CF3[AssemblyInfo.cs]
        CF4[Info.cs]
    end
    
    subgraph "Core Files"
        CORE1[Program.cs]
        CORE2[App.axaml]
        CORE3[ViewLocator.cs]
    end
    
    subgraph "ViewModels"
        VM1[主要窗口.cs]
        VM2[购买卡密VM.cs]
        VM3[检查限制.cs]
        VM4[获取设备标识.cs]
        VM5[重置机器码.cs]
        VM6[界面修改器.cs]
    end
    
    subgraph "Models"
        M1[CursorProcessManager.cs]
        M2[HttpService.cs]
        M3[LocalConfig.cs]
        M4[Tools.cs]
        M5[验证结果.cs]
    end
    
    subgraph "Views"
        V1[主要窗口.axaml]
        V2[购买卡密.axaml]
    end
    
    CF1 --> CORE1
    CF4 --> VM1
    CF4 --> VM2
    CF4 --> VM3
    
    CORE1 --> CORE2
    CORE2 --> CORE3
    CORE3 --> VM1
    
    VM1 --> VM3
    VM1 --> VM5
    VM1 --> M1
    VM2 --> M2
    VM2 --> M3
    VM3 --> VM4
    VM3 --> M2
    VM5 --> M1
    VM5 --> M3
    VM6 --> M1
    
    V1 --> VM1
    V2 --> VM2
```

## 第三方库依赖图

```mermaid
graph TB
    subgraph "NuGet Packages"
        PKG1[Avalonia 11.x]
        PKG2[Avalonia.Desktop]
        PKG3[Avalonia.Themes.Fluent]
        PKG4[CommunityToolkit.Mvvm 8.2.0]
        PKG5[MessageBox.Avalonia]
        PKG6[System.Text.Json]
        PKG7[System.Net.Http]
    end
    
    subgraph "Application Components"
        APP1[UI Components]
        APP2[MVVM Infrastructure]
        APP3[Network Services]
        APP4[Data Serialization]
        APP5[User Dialogs]
    end
    
    PKG1 --> APP1
    PKG2 --> APP1
    PKG3 --> APP1
    PKG4 --> APP2
    PKG5 --> APP5
    PKG6 --> APP4
    PKG7 --> APP3
    
    APP1 --> APP2
    APP2 --> APP3
    APP2 --> APP4
    APP2 --> APP5
```

## 平台特定依赖

```mermaid
graph LR
    subgraph "Common Code"
        CC1[CursorProcessManager]
        CC2[PlatformPaths]
        CC3[获取设备标识]
    end
    
    subgraph "Windows Specific"
        WIN1[Process.GetProcessesByName]
        WIN2[Registry APIs]
        WIN3[Environment.SpecialFolder]
        WIN4[NetworkInterface APIs]
    end
    
    subgraph "macOS Specific"
        MAC1[Shell Commands]
        MAC2[/Applications Path]
        MAC3[Library/Application Support]
        MAC4[pgrep/pkill Commands]
    end
    
    CC1 --> WIN1
    CC1 --> MAC1
    CC2 --> WIN3
    CC2 --> MAC3
    CC3 --> WIN4
    CC3 --> MAC4
```

## 编译时依赖

```mermaid
graph TD
    subgraph "Source Code"
        SRC1[*.cs Files]
        SRC2[*.axaml Files]
        SRC3[*.csproj File]
    end
    
    subgraph "Build Tools"
        BT1[MSBuild]
        BT2[Avalonia Compiler]
        BT3[C# Compiler]
        BT4[NuGet Package Manager]
    end
    
    subgraph "Generated Code"
        GEN1[CompiledAvaloniaXaml/]
        GEN2[MVVM Generated Code]
        GEN3[Assembly Metadata]
    end
    
    subgraph "Output"
        OUT1[CursorPro.exe]
        OUT2[Dependencies DLLs]
        OUT3[Resources]
    end
    
    SRC1 --> BT3
    SRC2 --> BT2
    SRC3 --> BT4
    
    BT1 --> GEN1
    BT3 --> GEN2
    BT4 --> GEN3
    
    GEN1 --> OUT1
    GEN2 --> OUT1
    GEN3 --> OUT2
    OUT3 --> OUT1
```

## 运行时依赖

```mermaid
graph TB
    subgraph "Runtime Environment"
        RT1[.NET 8.0 Runtime]
        RT2[Avalonia Runtime]
        RT3[Platform APIs]
    end
    
    subgraph "Application"
        APP[CursorPro.exe]
    end
    
    subgraph "External Resources"
        EXT1[Configuration Files]
        EXT2[Cursor Application]
        EXT3[Network APIs]
        EXT4[File System]
    end
    
    RT1 --> APP
    RT2 --> APP
    RT3 --> APP
    
    APP --> EXT1
    APP --> EXT2
    APP --> EXT3
    APP --> EXT4
```

## 依赖管理策略

### 1. 包版本管理
- 使用PackageReference管理NuGet包
- 固定主要版本号，允许补丁更新
- 定期更新安全补丁

### 2. 平台兼容性
- 使用条件编译处理平台差异
- 运行时检测操作系统类型
- 抽象平台特定功能

### 3. 循环依赖避免
- 清晰的分层架构
- 依赖注入减少耦合
- 接口抽象具体实现

### 4. 性能优化
- 延迟加载非关键依赖
- 缓存重复计算结果
- 异步操作避免阻塞

## 风险评估

### 高风险依赖
1. **Avalonia UI** - 核心UI框架，版本更新可能影响兼容性
2. **网络API** - 依赖外部服务器，存在可用性风险
3. **Cursor应用** - 目标应用更新可能破坏功能

### 中风险依赖
1. **系统API** - 操作系统更新可能影响功能
2. **第三方库** - 库更新可能引入破坏性变更

### 低风险依赖
1. **.NET Runtime** - 微软官方支持，稳定性高
2. **标准库** - 成熟稳定的基础库

## 依赖优化建议

1. **减少外部依赖**：评估每个依赖的必要性
2. **版本锁定**：关键依赖使用精确版本号
3. **备用方案**：为关键功能提供降级方案
4. **监控更新**：定期检查依赖更新和安全补丁
5. **文档维护**：保持依赖关系文档的及时更新