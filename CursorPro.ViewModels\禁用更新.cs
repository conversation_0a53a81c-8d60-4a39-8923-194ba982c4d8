using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using CursorPro.Models;

namespace CursorPro.ViewModels;

public class 禁用更新
{
	private readonly PlatformPaths _paths;

	public 禁用更新()
	{
		_paths = new PlatformPaths();
	}

	public 禁用更新(PlatformPaths paths)
	{
		_paths = paths ?? throw new ArgumentNullException("paths");
	}

	public bool 执行禁用更新()
	{
		try
		{
			删除更新程序目录();
			移除更新URL();
			return true;
		}
		catch (Exception)
		{
			return false;
		}
	}

	public bool 删除更新程序目录()
	{
		try
		{
			if (Directory.Exists(_paths.UpdaterPath))
			{
				try
				{
					Directory.Delete(_paths.UpdaterPath, recursive: true);
				}
				catch (Exception)
				{
				}
			}
			return true;
		}
		catch (Exception)
		{
			return false;
		}
	}

	public bool 移除更新URL()
	{
		try
		{
			if (File.Exists(_paths.ProductJsonPath))
			{
				try
				{
					using JsonDocument jsonDocument = JsonDocument.Parse(File.ReadAllText(_paths.ProductJsonPath));
					Dictionary<string, object> dictionary = new Dictionary<string, object>();
					foreach (JsonProperty item in jsonDocument.RootElement.EnumerateObject())
					{
						if (item.Name != "updateUrl" && item.Name != "updateChannel")
						{
							dictionary[item.Name] = item.Value.Clone();
						}
					}
					string contents = JsonSerializer.Serialize(dictionary, new JsonSerializerOptions
					{
						WriteIndented = true
					});
					FileHelper.EnsureFileWritable(_paths.ProductJsonPath);
					File.WriteAllText(_paths.ProductJsonPath, contents);
				}
				catch (Exception)
				{
				}
			}
			return true;
		}
		catch (Exception)
		{
			return false;
		}
	}

	public (bool HasUpdaterDirectory, bool HasProductJson, bool HasUpdateConfig) 检查更新文件状态()
	{
		try
		{
			bool item = Directory.Exists(_paths.UpdaterPath);
			bool flag = File.Exists(_paths.ProductJsonPath);
			bool item2 = false;
			if (flag)
			{
				try
				{
					using JsonDocument jsonDocument = JsonDocument.Parse(File.ReadAllText(_paths.ProductJsonPath));
					item2 = jsonDocument.RootElement.TryGetProperty("updateUrl", out var value) || jsonDocument.RootElement.TryGetProperty("updateChannel", out value);
				}
				catch (Exception)
				{
				}
			}
			return (HasUpdaterDirectory: item, HasProductJson: flag, HasUpdateConfig: item2);
		}
		catch (Exception)
		{
			return (HasUpdaterDirectory: false, HasProductJson: false, HasUpdateConfig: false);
		}
	}

	public string 获取更新程序目录路径()
	{
		return _paths.UpdaterPath;
	}

	public string 获取产品配置文件路径()
	{
		return _paths.ProductJsonPath;
	}

	public bool 验证禁用更新结果()
	{
		try
		{
			var (flag, _, flag2) = 检查更新文件状态();
			return !flag && !flag2;
		}
		catch (Exception)
		{
			return false;
		}
	}
}
