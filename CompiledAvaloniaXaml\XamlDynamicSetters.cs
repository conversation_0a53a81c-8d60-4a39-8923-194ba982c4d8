using System;
using System.Runtime.CompilerServices;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Controls.Presenters;
using Avalonia.Data;
using Avalonia.Media;

namespace CompiledAvaloniaXaml;

[CompilerGenerated]
internal class XamlDynamicSetters
{
	public static void _003C_003EXamlDynamicSetter_1(ContentPresenter P_0, BindingPriority P_1, IBinding P_2)
	{
		if (P_2 != null)
		{
			IBinding binding = P_2;
			AvaloniaObjectExtensions.Bind(P_0, ContentPresenter.ContentProperty, binding);
		}
		else
		{
			object value = P_2;
			int priority = (int)P_1;
			P_0.SetValue(ContentPresenter.ContentProperty, value, (BindingPriority)priority);
		}
	}

	public static void _003C_003EXamlDynamicSetter_2(PathIcon P_0, object P_1)
	{
		if (P_1 is UnsetValueType)
		{
			P_0.SetValue(PathIcon.DataProperty, AvaloniaProperty.UnsetValue);
			return;
		}
		if (P_1 is IBinding)
		{
			IBinding binding = (IBinding)P_1;
			AvaloniaObjectExtensions.Bind(P_0, PathIcon.DataProperty, binding);
			return;
		}
		if (P_1 is Geometry)
		{
			P_0.Data = (Geometry?)P_1;
			return;
		}
		if (P_1 == null)
		{
			P_0.Data = (Geometry?)P_1;
			return;
		}
		throw new InvalidCastException();
	}
}
