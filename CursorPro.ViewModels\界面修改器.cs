using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using CursorPro.Models;

namespace CursorPro.ViewModels;

public class 界面修改器
{
	private static readonly Dictionary<string, string> EMOJI = new Dictionary<string, string>
	{
		{ "FILE", "\ud83d\udcc4" },
		{ "BACKUP", "\ud83d\udcbe" },
		{ "SUCCESS", "✅" },
		{ "ERROR", "❌" },
		{ "INFO", "ℹ\ufe0f" },
		{ "RESET", "\ud83d\udd04" },
		{ "WARNING", "⚠\ufe0f" }
	};

	private static string 获取用户文档路径()
	{
		return Environment.GetFolderPath(Environment.SpecialFolder.Personal);
	}

	public static string 获取Cursor主文件路径()
	{
		string 平台类型 = Info.平台类型;
		Path.Combine(Path.Combine(获取用户文档路径(), ".cursor-free-everyday"), "config.ini");
		Dictionary<string, Dictionary<string, string>> dictionary = new Dictionary<string, Dictionary<string, string>>
		{
			{
				"Mac",
				new Dictionary<string, string>
				{
					{ "base", "/Applications/Cursor.app/Contents/Resources/app" },
					{ "main", "out/vs/workbench/workbench.desktop.main.js" }
				}
			},
			{
				"Windows",
				new Dictionary<string, string> { { "main", "out\\vs\\workbench\\workbench.desktop.main.js" } }
			}
		};
		if (!dictionary.ContainsKey(平台类型))
		{
			throw new PlatformNotSupportedException("不支持的操作系统: " + 平台类型);
		}
		string text;
		if (平台类型 == "Windows")
		{
			text = 查找Windows版Cursor安装路径();
			if (string.IsNullOrEmpty(text))
			{
				text = LocalConfig.读取("cursor_path") ?? string.Empty;
				if (string.IsNullOrEmpty(text))
				{
					throw new FileNotFoundException("无法找到Cursor安装目录，请手动指定路径");
				}
			}
		}
		else
		{
			if (!(平台类型 == "Mac"))
			{
				throw new PlatformNotSupportedException("不支持的操作系统: " + 平台类型);
			}
			text = dictionary[平台类型]["base"];
			string text2 = LocalConfig.读取("cursor_path");
			if (!string.IsNullOrEmpty(text2))
			{
				text = text2;
			}
		}
		string text3 = Path.Combine(text, dictionary[平台类型]["main"]);
		if (!File.Exists(text3))
		{
			throw new FileNotFoundException("未找到Cursor主文件: " + text3);
		}
		return text3;
	}

	private static string 查找Windows版Cursor安装路径()
	{
		try
		{
			string text = LocalConfig.读取("cursorPath");
			if (string.IsNullOrEmpty(text) || !File.Exists(text))
			{
				throw new FileNotFoundException("无法找到Cursor主程序路径，请先启动Cursor后再试");
			}
			string? obj = Path.GetDirectoryName(text) ?? string.Empty;
			if (string.IsNullOrEmpty(obj))
			{
				throw new DirectoryNotFoundException("无法解析Cursor主程序目录");
			}
			string text2 = Path.Combine(obj, "resources", "app");
			if (Directory.Exists(text2))
			{
				return text2;
			}
			throw new DirectoryNotFoundException("未找到Cursor资源目录: " + text2);
		}
		catch (Exception)
		{
			string[] array = new string[3]
			{
				Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Programs", "Cursor", "resources", "app"),
				Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), "Cursor", "resources", "app"),
				Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86), "Cursor", "resources", "app")
			};
			foreach (string text3 in array)
			{
				if (Directory.Exists(text3))
				{
					return text3;
				}
			}
			return string.Empty;
		}
	}

	public static bool 修改Workbench文件(string 文件路径)
	{
		try
		{
			FileAttributes attributes = File.GetAttributes(文件路径);
			string tempFileName = Path.GetTempFileName();
			try
			{
				string text;
				using (StreamReader streamReader = new StreamReader(文件路径, Encoding.UTF8))
				{
					text = streamReader.ReadToEnd();
				}
				string text2 = text;
				string text3 = "title:\"Upgrade to Pro\"";
				if (text2.IndexOf(text3) >= 0)
				{
					text2 = text2.Replace(text3, "title:\"Business Pro\"");
				}
				foreach (KeyValuePair<string, string> item in new Dictionary<string, string>
				{
					{ "<div>Pro Trial", "<div>Pro" },
					{ "notifications-toasts", "notifications-toasts hidden" }
				})
				{
					string key = item.Key;
					string value = item.Value;
					if (text2.Contains(key))
					{
						text2 = text2.Replace(key, value);
						_ = text2 != text;
					}
				}
				using (StreamWriter streamWriter = new StreamWriter(tempFileName, append: false, Encoding.UTF8))
				{
					streamWriter.Write(text2);
				}
				string text4 = DateTime.Now.ToString("yyyyMMdd_HHmmss");
				string destFileName = 文件路径 + ".backup." + text4;
				File.Copy(文件路径, destFileName, overwrite: true);
				if (File.Exists(文件路径))
				{
					File.Delete(文件路径);
				}
				File.Move(tempFileName, 文件路径);
				File.SetAttributes(文件路径, attributes);
				return true;
			}
			catch (Exception)
			{
				if (File.Exists(tempFileName))
				{
					try
					{
						File.Delete(tempFileName);
					}
					catch
					{
					}
				}
				return false;
			}
		}
		catch (Exception)
		{
			return false;
		}
	}

	public static bool 执行修改界面()
	{
		try
		{
			if (修改Workbench文件(获取Cursor主文件路径()))
			{
				return true;
			}
			return false;
		}
		catch (Exception)
		{
			return false;
		}
	}

	public static Task<bool> 从重置机器码调用()
	{
		return Task.FromResult(执行修改界面());
	}
}
