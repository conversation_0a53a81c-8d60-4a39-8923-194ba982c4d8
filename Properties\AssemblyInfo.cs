using System.Diagnostics;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.Versioning;

[assembly: AssemblyCompany("CursorPro")]
[assembly: AssemblyConfiguration("Release")]
[assembly: AssemblyFileVersion("1.0.0.0")]
[assembly: AssemblyInformationalVersion("1.0.0+0216b9f6395b912d4abb84849ea2405a73e3c2f9")]
[assembly: AssemblyProduct("CursorPro")]
[assembly: AssemblyTitle("CursorPro")]
[assembly: AssemblyMetadata("AvaloniaUseCompiledBindingsByDefault", "True")]
[assembly: AssemblyVersion("1.0.0.0")]
// [module: RefSafetyRules(11)] // 注释掉编译器保留的属性
