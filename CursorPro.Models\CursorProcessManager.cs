using System;
using System.Diagnostics;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;

namespace CursorPro.Models;

public static class CursorProcessManager
{
	private static string? _cachedCursorPath;

	private static string? _cachedInstallPath;

	public static void InitializeCursorPathCache()
	{
		try
		{
			_cachedCursorPath = GetCursorPath();
			string.IsNullOrEmpty(_cachedCursorPath);
		}
		catch (Exception)
		{
			_cachedCursorPath = null;
		}
	}

	public static string? GetCachedCursorPath()
	{
		if (!string.IsNullOrEmpty(_cachedCursorPath) && File.Exists(_cachedCursorPath))
		{
			return _cachedCursorPath;
		}
		_cachedCursorPath = GetCursorPath();
		string.IsNullOrEmpty(_cachedCursorPath);
		return _cachedCursorPath;
	}

	public static string? GetCachedInstallPath()
	{
		if (!string.IsNullOrEmpty(_cachedInstallPath) && Directory.Exists(_cachedInstallPath))
		{
			if (File.Exists(Path.Combine(_cachedInstallPath, "Cursor.exe")))
			{
				return _cachedInstallPath;
			}
			_cachedInstallPath = null;
		}
		string cachedCursorPath = GetCachedCursorPath();
		if (!string.IsNullOrEmpty(cachedCursorPath))
		{
			_cachedInstallPath = Path.GetDirectoryName(cachedCursorPath);
			return _cachedInstallPath;
		}
		return null;
	}

	public static async Task<bool> CloseCursorAsync()
	{
		try
		{
			if (OperatingSystem.IsWindows())
			{
				return CloseWindowsCursor();
			}
			if (OperatingSystem.IsMacOS())
			{
				return await CloseMacCursorAsync();
			}
			return false;
		}
		catch (Exception)
		{
			return false;
		}
	}

	public static string? GetCursorPath()
	{
		try
		{
			if (OperatingSystem.IsWindows())
			{
				return GetDefaultWindowsCursorPath();
			}
			if (OperatingSystem.IsMacOS())
			{
				return GetDefaultMacCursorPath();
			}
			return null;
		}
		catch (Exception)
		{
			return null;
		}
	}

	public static async Task<bool> StartCursorAsync(string? cursorPath = null)
	{
		try
		{
			if (OperatingSystem.IsWindows())
			{
				return StartWindowsCursor(cursorPath);
			}
			if (OperatingSystem.IsMacOS())
			{
				return await StartMacCursorAsync(cursorPath);
			}
			return false;
		}
		catch (Exception)
		{
			return false;
		}
	}

	private static bool CloseWindowsCursor()
	{
		try
		{
			Process[] processesByName = Process.GetProcessesByName("Cursor");
			if (processesByName.Length == 0)
			{
				return true;
			}
			processesByName = Process.GetProcessesByName("Cursor");
			if (processesByName.Length != 0)
			{
				Process[] array = processesByName;
				foreach (Process process in array)
				{
					try
					{
						if (!process.HasExited)
						{
							process.Kill();
						}
					}
					catch (Exception)
					{
					}
				}
			}
			int num = 0;
			int num2 = 20;
			int millisecondsTimeout = 100;
			for (int j = 0; j < num2; j++)
			{
				num = 0;
				processesByName = Process.GetProcessesByName("Cursor");
				Process[] array = processesByName;
				foreach (Process process2 in array)
				{
					try
					{
						if (!process2.HasExited)
						{
							num++;
						}
					}
					catch (Exception)
					{
					}
				}
				if (num == 0)
				{
					break;
				}
				if (j < num2 - 1)
				{
					Thread.Sleep(millisecondsTimeout);
				}
			}
			return num == 0;
		}
		catch (Exception)
		{
			return false;
		}
	}

	private static bool StartWindowsCursor(string? cursorPath)
	{
		try
		{
			if (string.IsNullOrEmpty(cursorPath))
			{
				cursorPath = GetDefaultWindowsCursorPath();
				if (string.IsNullOrEmpty(cursorPath))
				{
					return false;
				}
			}
			if (StartCursorAsNormalUser(cursorPath))
			{
				return true;
			}
			Process.Start(new ProcessStartInfo
			{
				FileName = cursorPath,
				UseShellExecute = true
			});
			return true;
		}
		catch (Exception)
		{
			return false;
		}
	}

	private static bool StartCursorAsNormalUser(string cursorPath)
	{
		try
		{
			if (Process.Start(new ProcessStartInfo
			{
				FileName = "explorer.exe",
				Arguments = "\"" + cursorPath + "\"",
				UseShellExecute = false,
				CreateNoWindow = true
			}) != null)
			{
				return true;
			}
			if (Process.Start(new ProcessStartInfo
			{
				FileName = "cmd.exe",
				Arguments = "/c start \"\" \"" + cursorPath + "\"",
				UseShellExecute = false,
				CreateNoWindow = true,
				WindowStyle = ProcessWindowStyle.Hidden
			}) != null)
			{
				return true;
			}
			return false;
		}
		catch (Exception)
		{
			return false;
		}
	}

	private static string? GetDefaultWindowsCursorPath()
	{
		try
		{
			if (!string.IsNullOrEmpty(_cachedCursorPath) && File.Exists(_cachedCursorPath))
			{
				return _cachedCursorPath;
			}
			string text = LocalConfig.读取("cursor_path");
			if (!string.IsNullOrEmpty(text) && File.Exists(text))
			{
				_cachedCursorPath = text;
				return text;
			}
			if (!string.IsNullOrEmpty(text))
			{
				LocalConfig.写入("cursor_path", "");
			}
			Process[] processesByName = Process.GetProcessesByName("Cursor");
			if (processesByName.Length != 0)
			{
				string text2 = processesByName[0].MainModule?.FileName;
				if (!string.IsNullOrEmpty(text2) && File.Exists(text2))
				{
					LocalConfig.写入("cursor_path", text2);
					_cachedCursorPath = text2;
					return text2;
				}
			}
			string cursorPathFromDesktop = GetCursorPathFromDesktop();
			if (!string.IsNullOrEmpty(cursorPathFromDesktop))
			{
				LocalConfig.写入("cursor_path", cursorPathFromDesktop);
				_cachedCursorPath = cursorPathFromDesktop;
				return cursorPathFromDesktop;
			}
			return null;
		}
		catch (Exception)
		{
			return null;
		}
	}

	private static string? GetCursorPathFromDesktop()
	{
		try
		{
			string[] array = new string[2]
			{
				Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "Cursor.lnk"),
				Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonDesktopDirectory), "Cursor.lnk")
			};
			foreach (string text in array)
			{
				try
				{
					if (File.Exists(text))
					{
						string shortcutTarget = GetShortcutTarget(text);
						if (!string.IsNullOrEmpty(shortcutTarget) && File.Exists(shortcutTarget))
						{
							return shortcutTarget;
						}
					}
				}
				catch (Exception)
				{
				}
			}
			return null;
		}
		catch (Exception)
		{
			return null;
		}
	}

	private static string? GetShortcutTarget(string shortcutPath)
	{
		try
		{
			if (OperatingSystem.IsWindows())
			{
				return GetShortcutTargetWindows(shortcutPath);
			}
			return null;
		}
		catch (Exception)
		{
			return null;
		}
	}

	private static string? GetShortcutTargetWindows(string shortcutPath)
	{
		if (!OperatingSystem.IsWindows())
		{
			return null;
		}
		try
		{
			Type typeFromProgID = Type.GetTypeFromProgID("WScript.Shell");
			if (typeFromProgID == null)
			{
				return null;
			}
			dynamic val = Activator.CreateInstance(typeFromProgID);
			if (val == null)
			{
				return null;
			}
			dynamic val2 = val.CreateShortcut(shortcutPath);
			string text = val2.TargetPath;
			if (val2 != null)
			{
				Marshal.ReleaseComObject(val2);
			}
			if (val != null)
			{
				Marshal.ReleaseComObject(val);
			}
			return string.IsNullOrEmpty(text) ? null : text;
		}
		catch (Exception)
		{
			return null;
		}
	}

	private static string? GetDefaultMacCursorPath()
	{
		try
		{
			string text = "/Applications/Cursor.app";
			if (Directory.Exists(text))
			{
				return text;
			}
			return null;
		}
		catch (Exception)
		{
			return null;
		}
	}

	private static async Task<bool> CloseMacCursorAsync()
	{
		_ = 3;
		try
		{
			string text = await ExecuteCommand("pgrep", "-f Cursor.app", "检查初始Cursor进程", throwOnError: false);
			if (string.IsNullOrWhiteSpace(text))
			{
				return true;
			}
			text.Trim().Split('\n', StringSplitOptions.RemoveEmptyEntries);
			await ExecuteCommand("pkill", "-KILL -f Cursor.app", "强制终止Cursor进程");
			int runningProcessCount = 0;
			int maxCheckAttempts = 20;
			int checkInterval = 100;
			for (int attempt = 0; attempt < maxCheckAttempts; attempt++)
			{
				string text2 = await ExecuteCommand("pgrep", "-f Cursor.app", "检查Cursor进程", throwOnError: false);
				if (string.IsNullOrWhiteSpace(text2))
				{
					runningProcessCount = 0;
					break;
				}
				string[] array = text2.Trim().Split('\n', StringSplitOptions.RemoveEmptyEntries);
				runningProcessCount = array.Length;
				if (attempt < maxCheckAttempts - 1)
				{
					await Task.Delay(checkInterval);
				}
			}
			return runningProcessCount == 0;
		}
		catch (Exception)
		{
			return false;
		}
	}

	private static async Task<bool> StartMacCursorAsync(string? cursorPath)
	{
		_ = 1;
		try
		{
			try
			{
				await ExecuteCommand("open", "-a Cursor", "启动Cursor应用");
				return true;
			}
			catch (Exception)
			{
			}
			try
			{
				string text = ((!string.IsNullOrEmpty(cursorPath)) ? cursorPath : "/Applications/Cursor.app");
				await ExecuteCommand("open", "\"" + text + "\"", "启动Cursor应用");
				return true;
			}
			catch (Exception)
			{
				return false;
			}
		}
		catch (Exception)
		{
			return false;
		}
	}

	private static async Task<string> ExecuteCommand(string command, string arguments, string description, bool throwOnError = true)
	{
		_ = 2;
		try
		{
			ProcessStartInfo startInfo = new ProcessStartInfo
			{
				FileName = command,
				Arguments = arguments,
				UseShellExecute = false,
				RedirectStandardOutput = true,
				RedirectStandardError = true,
				CreateNoWindow = true
			};
			using Process process = Process.Start(startInfo);
			if (process == null)
			{
				throw new Exception("无法启动进程: " + command);
			}
			await process.WaitForExitAsync();
			string output = await process.StandardOutput.ReadToEndAsync();
			string value = await process.StandardError.ReadToEndAsync();
			if (process.ExitCode != 0 && throwOnError)
			{
				throw new Exception($"命令执行失败 (退出码: {process.ExitCode}): {value}");
			}
			return output;
		}
		catch (Exception)
		{
			if (throwOnError)
			{
				throw;
			}
			return string.Empty;
		}
	}
}
