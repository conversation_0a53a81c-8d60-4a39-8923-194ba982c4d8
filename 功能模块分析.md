# CursorPro 功能模块分析文档

## 项目概述

CursorPro 是一个针对 Cursor 代码编辑器的增强工具，通过反编译技术开发。该项目主要功能是绕过 Cursor 编辑器的试用限制，提供付费激活服务，并对 Cursor 界面进行定制化修改。

## 核心功能模块

### 1. 账号验证系统
- **试用账号验证**：通过机器码验证试用资格
- **付费账号验证**：通过卡密激活付费功能
- **版本更新检查**：自动检查并提示版本更新

### 2. 设备标识管理
- **机器码生成**：基于MAC地址或随机生成唯一标识
- **机器码重置**：清除Cursor配置文件，重置设备标识
- **本地配置管理**：存储和管理本地配置信息

### 3. Cursor进程管理
- **进程检测**：检测Cursor是否运行
- **进程启动**：跨平台启动Cursor应用
- **进程关闭**：安全关闭Cursor进程
- **路径管理**：自动检测和缓存Cursor安装路径

### 4. 界面修改器
- **Workbench文件修改**：修改Cursor主界面文件
- **付费提示隐藏**：移除或修改付费相关提示
- **界面定制**：自定义Cursor界面元素

### 5. 购买卡密系统
- **支付链接生成**：创建支付订单链接
- **订单状态查询**：检查支付状态
- **卡密激活**：处理卡密激活流程
- **购买记录恢复**：根据设备信息恢复购买记录

### 6. 网络通信模块
- **HTTP服务**：处理与服务器的通信
- **API调用**：封装各种API接口调用
- **错误处理**：网络异常和错误处理

## 项目流程图

```mermaid
graph TD
    A[启动CursorPro] --> B[检查本地配置]
    B --> C{是否有卡密?}
    C -->|有| D[验证付费账号]
    C -->|无| E[验证试用账号]
    
    D --> F{验证成功?}
    E --> G{验证成功?}
    
    F -->|是| H[显示主界面]
    F -->|否| I[显示错误信息]
    G -->|是| H
    G -->|否| J[显示购买界面]
    
    H --> K[用户选择功能]
    K --> L{选择什么功能?}
    
    L -->|一键获取额度| M[重置机器码流程]
    L -->|加速Cursor| N[修改Cursor配置]
    L -->|购买卡密| O[跳转购买流程]
    
    M --> M1[关闭Cursor进程]
    M1 --> M2[清除配置文件]
    M2 --> M3[重新生成机器码]
    M3 --> M4[重启Cursor]
    M4 --> P[操作完成]
    
    N --> N1[检测Cursor路径]
    N1 --> N2[修改Workbench文件]
    N2 --> N3[隐藏付费提示]
    N3 --> P
    
    O --> O1[生成支付链接]
    O1 --> O2[用户完成支付]
    O2 --> O3[验证卡密]
    O3 --> O4[保存激活信息]
    O4 --> H
    
    J --> O
    I --> Q[退出程序]
    P --> R[返回主界面]
    R --> K
```

## 技术架构流程

```mermaid
graph LR
    A[UI层 - Avalonia] --> B[ViewModel层 - MVVM]
    B --> C[Service层 - 业务逻辑]
    C --> D[Model层 - 数据模型]
    
    C --> E[HttpService - 网络通信]
    C --> F[LocalConfig - 本地配置]
    C --> G[CursorProcessManager - 进程管理]
    
    E --> H[远程API服务器]
    F --> I[本地配置文件]
    G --> J[Cursor应用程序]
```

## 数据流向图

```mermaid
sequenceDiagram
    participant U as 用户
    participant UI as CursorPro界面
    participant VM as ViewModel
    participant API as 远程API
    participant Cursor as Cursor应用
    
    U->>UI: 启动程序
    UI->>VM: 初始化
    VM->>API: 验证账号
    API-->>VM: 返回验证结果
    VM-->>UI: 更新界面状态
    
    U->>UI: 点击"一键获取额度"
    UI->>VM: 执行重置机器码
    VM->>Cursor: 关闭进程
    VM->>VM: 清除配置文件
    VM->>VM: 重新生成机器码
    VM->>Cursor: 重启应用
    VM-->>UI: 操作完成
    
    U->>UI: 点击"购买卡密"
    UI->>VM: 创建支付订单
    VM->>API: 生成支付链接
    API-->>VM: 返回支付信息
    VM-->>UI: 显示支付界面
```

## 安全机制

### 1. 设备绑定
- 基于MAC地址生成唯一机器码
- 防止卡密在多设备使用

### 2. 服务器验证
- 实时验证账号状态
- 检查版本更新和兼容性

### 3. 进程保护
- 安全关闭Cursor进程
- 备份和恢复配置文件

## 商业模式

### 1. 免费试用
- 基于设备标识的试用限制
- 可通过重置机器码延长试用

### 2. 付费激活
- 卡密激活系统
- 支持周卡、月卡等多种套餐

### 3. 代理推广
- 内置代理标识系统
- 支持推广分成机制