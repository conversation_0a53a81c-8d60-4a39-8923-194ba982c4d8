using System;
using System.Text.Json;
using System.Threading.Tasks;
using CursorPro.Models;

namespace CursorPro.ViewModels;

public class 检查限制
{
	private readonly 获取设备标识 _设备标识 = new 获取设备标识();

	public async Task<验证结果> 验证试用账号()
	{
		try
		{
			string machine_code = _设备标识.获取机器码();
			var anon = new
			{
				operation = "verify_trial",
				machine_code = machine_code,
				version = Info.本地版本号,
				platform = Info.平台类型
			};
			JsonSerializer.Serialize(anon);
			try
			{
				验证结果 验证结果2 = await HttpService.PostAsync<object, 验证结果>(Info.API基础地址 + ":" + Info.免费服务端口 + "/api/account", anon);
				if (验证结果2 != null && 验证结果2.NeedUpdate == true)
				{
					处理版本更新();
					return 验证结果2;
				}
				return 验证结果2 ?? new 验证结果
				{
					Success = false,
					Message = "解析响应失败"
				};
			}
			catch (Exception)
			{
				return 创建失败结果("网络连接失败，请检查网络后重试");
			}
		}
		catch (Exception ex2)
		{
			return 创建失败结果("验证出错: " + ex2.Message);
		}
	}

	public async Task<验证结果> 验证付费账号(string 卡密)
	{
		try
		{
			string machine_code = _设备标识.获取机器码();
			var anon = new
			{
				operation = "verify_paid",
				key = 卡密,
				machine_code = machine_code,
				version = Info.本地版本号,
				platform = Info.平台类型,
				app_name = Info.AppName,
				remark = Info.代理
			};
			JsonSerializer.Serialize(anon);
			try
			{
				验证结果 验证结果2 = await HttpService.PostAsync<object, 验证结果>(Info.API基础地址 + ":" + Info.付费服务端口 + "/api/paid", anon);
				if (验证结果2 != null && 验证结果2.NeedUpdate == true)
				{
					处理版本更新();
					return 验证结果2;
				}
				if (验证结果2 != null && 验证结果2.Success)
				{
					LocalConfig.写入("CardKey", 卡密);
					if (!string.IsNullOrEmpty(验证结果2.ExpiryTime))
					{
						LocalConfig.写入("ExpiryTime", 验证结果2.ExpiryTime);
						Info.到期时间 = 验证结果2.ExpiryTime;
					}
				}
				return 验证结果2 ?? new 验证结果
				{
					Success = false,
					Message = "解析响应失败"
				};
			}
			catch (Exception)
			{
				return 创建失败结果("网络连接失败，请检查网络后重试");
			}
		}
		catch (Exception ex2)
		{
			return 创建失败结果("验证出错: " + ex2.Message);
		}
	}

	public async Task<验证结果> 启动检查()
	{
		_ = 1;
		try
		{
			await new 购买卡密VM().检查并处理本地订单();
			return await 执行验证();
		}
		catch (Exception ex)
		{
			return 创建失败结果("启动检查出错: " + ex.Message);
		}
	}

	private async Task<验证结果> 执行验证()
	{
		string text = LocalConfig.读取("CardKey");
		if (!string.IsNullOrEmpty(text))
		{
			return await 验证付费账号(text);
		}
		return await 验证试用账号();
	}

	private static 验证结果 创建失败结果(string 错误消息)
	{
		return new 验证结果
		{
			Success = false,
			Message = 错误消息
		};
	}

	private void 处理版本更新()
	{
		try
		{
			Tools.OpenUrl(Info.获取下载链接());
		}
		catch (Exception)
		{
		}
	}
}
